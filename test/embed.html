<!DOCTYPE html>
<html>
  <head>
    <title>Test: OSC</title>
  </head>
  <body style="margin:0">
    <div id="container" style="width:100%;height:100vh"></div>

    <script src="http://localhost:3003/static/js/embed.js"></script>
    <script>
      ( function(){
        const options = {
          // Development environment flag
          dev: true,
          // Access credentials
          clientId: 'j2u1u2u1uy2g21',
          accessToken: '1PFuMQTVYgRBC77DRKk2I3Q8Vj1Izb3XUey7Wko3FkDznCOYTqmIRDjoNB70h5t1JnPc',
          // Initial patient (Optional)
          patient: {
            id: '**********',
            name: '<PERSON><PERSON><PERSON>'
          }
        }

        this.osc = new SDK.OSC( 'container', options )
        this.osc
        .ready( function( details ){
          // Tenant details
          console.log('Tenant Information: ', details )

          // Set/Update patient dataset
          this.setPatient({
            id: '**********',
            name: 'Commando Jark'
          })

          // UI sections navigation control (Default: `options`)
          // this.open('admin')
        } )
        .error( error => console.log('[Error]: ', error ) )
        .failed( response => console.log('[Failed]: ', response ) )
        .done( response => console.log('[Done]: ', response ) )
      } )()
    </script>
  </body>
</html>