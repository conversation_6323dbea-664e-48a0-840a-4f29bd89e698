name: Upswing Prod Server Deployment

on:
  push:
    branches:
    - 'prod-branch'
  workflow_dispatch:      

jobs:
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    environment: Prod

    steps:
    - name: Checkout
      uses: actions/checkout@v3

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1

    - name: executing remote ssh commands using ssh key
      uses: appleboy/ssh-action@master
      with:
           host: "*************"
           username: "ubuntu"
           key: ${{ secrets.EC2_SSH_KEY }}
           port: "22"
           script: |
            cd /home/<USER>/symptom-checker
            git pull
            sh deploy.sh