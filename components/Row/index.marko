
<div.row 
    id=input.id 
    class=input.class 
    style=input.style
    on-click( () => component.emit('click') )>
  <if( input.col )>
    <for|col, index| of=input.col>
      <!-- `class="col" is default` -->
      <div key=index class="col" ...col>
        <if( col )><${col.renderBody} key=`row-${index}`/></if>
      </div>
    </for>
  </if>
  <else><${input ? input.renderBody : null}/></else>
</div>
