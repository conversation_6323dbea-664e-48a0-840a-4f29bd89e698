
class {
  onCreate(){ this.state = { show: false } }
  onShowMenu( status, e ){
    this.state.show = status
    status === false && this.emit('dismiss')
  }
}

<!-- Option Menu toggler -->
<Bx class=input.class
    type=`dots-${input.vertical ? 'vertical' : 'horizontal'}-rounded`
    on-click('onShowMenu', true )/>

<!-- Dropdown menu -->
<if( state.show )>
  <ul.list-unstyled.position-absolute.top-0.border.round-xs.overflow-hidden.theme-bg.m-25.animated.fadeIn.faster
      class=(input.right ? 'right-0' : 'left-0')
      style="min-width:180px;z-index:1000">
    <for|{ id, label, fn, shortcut, divider, important }, index| of=input.menu>
      <li.position-relative.nav-item.cursor-pointer.px-2.py-1.d-flex.align-items-center
          key=index
          class=(divider ? 'border-top' : false)
          on-click( () => { typeof fn == 'function' && fn( id ) } )>

        <!-- Menu option label -->
        <Locale.font-small-4 
                class=(important ? 'text-danger' : false)
                text=label/>
        <!-- Shortcut Command -->
        <if( shortcut )>
          <span.position-absolute.right-0.px-1.text-muted>
            <Bx.font-small-1.text-muted type="command"/> + <span.font-small-3.text-muted>B</span>
          </span>
        </if>
      </li>
    </for>
  </ul>

  <!-- Close opened drowpdown -->
  <subscribe to=document once-click('onShowMenu', false )/>
</if>