
class {
  onCreate(){ this.state = { targetedOption: null } }
  onMount(){

    const
    $this = $(this.getEl()),
    $activeOption = $this.find('.sw-option.active')

    this.$target = $this.find('.sw-target')

    // Initial Target width
    this.$target.width( $activeOption.width() +'px' )
    // Initial Target position
    const position = $activeOption.position()
    this.$target.css( 'left', ( position ? position.left : 0 )+'px' )
  }
  onInput( input ){

    let targetedOption = false

    // Activate targetedOption option
    for( let option in input.options ){
      if( input.options[ option ].selected ){
        this.state.targetedOption = input.options[ option ].value
        targetedOption = true
        break
      }
    }

    if( !targetedOption )
      this.state.targetedOption = input.options[0].value
  }

  onSwitch( value, index, e ){
    if( this.state.targetedOption == value ) return

    // New switch
    this.state.targetedOption = value

    // Move target to new activated option position
    const $activeOption = $(e.target).hasClass('.sw-option') ? $(e.target) : $(e.target).closest('.sw-option')
    if( !$activeOption.length ) return

    this.$target.width( $activeOption.width() +'px' )
    // Move target to active option's position
    const position = $activeOption.position()
    this.$target.css('left', ( position ? position.left : 0 )+'px' )

    // Return selected option
    this.emit('switch', value, index )
  }
}

<if( input.options && input.options.length )>
  <div.sw class=`${input.class} ${input.bg || 'theme-bg-fade'} ${input.color || 'white'}-text`>
    <div.sw-target.theme-bg.round-sm.theme-shadow></div>
    <div.sw-options.border.round-sm.d-flex.flex-nowrap.align-items-center.justify-content-around>
      <for|{ label, icon, value }, index| of=input.options>
        $ const isActive = state.targetedOption == value

        <div.sw-option
            class=`${input.color || 'grey'}-text ${isActive ? 'active' : ''}`
            key=index
            title=( !isActive ? Locale('Switch to '+ (label || '').toCapitalCase() ) : '')
            on-click('onSwitch', value, index )>
          <if( icon )><i class=icon style="color:inherit"></i></if>
          <else><Locale text=label/></else>
        </div>
      </for>
    </div>
  </div>
</if>
