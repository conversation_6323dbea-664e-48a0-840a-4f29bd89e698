// Simple API Key Authentication Middleware
export const requireApiKey = (req, res, next) => {
  const apiKey = req.headers['x-api-key'] || req.query.api_key;
  
  if (!apiKey) {
    return res.status(401).json({
      error: true,
      status: 'AUTH::MISSING_API_KEY',
      message: 'API key is required'
    });
  }
  
  // Check against environment variable or hardcoded keys
  const validApiKeys = [
    process.env.API_KEY,
    'dev-api-key-123', // For development
    'admin-key-456'    // For admin access
  ].filter(Boolean);
  
  if (!validApiKeys.includes(apiKey)) {
    return res.status(401).json({
      error: true,
      status: 'AUTH::INVALID_API_KEY',
      message: 'Invalid API key'
    });
  }
  
  next();
};

// Physician Authentication Middleware
export const requirePhysician = (req, res, next) => {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      error: true,
      status: 'AUTH::MISSING_TOKEN',
      message: 'Bearer token is required'
    });
  }
  
  const token = authHeader.substring(7); // Remove 'Bearer ' prefix
  
  try {
    // Simple base64 decode for demo (use JWT in production)
    const decoded = JSON.parse(Buffer.from(token, 'base64').toString());
    
    if (!decoded.name || !decoded.type || decoded.type !== 'physician') {
      throw new Error('Invalid token structure');
    }
    
    // Verify physician exists
    const Physicians = require('../../../src/data/physicians.json');
    const physician = Physicians.find(p => p.name === decoded.name);
    
    if (!physician) {
      throw new Error('Physician not found');
    }
    
    // Add physician info to request
    req.physician = { name: decoded.name };
    next();
    
  } catch (error) {
    return res.status(401).json({
      error: true,
      status: 'AUTH::INVALID_TOKEN',
      message: 'Invalid or expired token'
    });
  }
};

// Tenant Authentication Middleware
export const requireTenant = (req, res, next) => {
  const tenantId = req.headers['x-tenant-id'];
  
  if (!tenantId) {
    return res.status(401).json({
      error: true,
      status: 'AUTH::MISSING_TENANT',
      message: 'Tenant ID is required'
    });
  }
  
  // Add tenant info to request
  req.tenantId = tenantId;
  next();
};
