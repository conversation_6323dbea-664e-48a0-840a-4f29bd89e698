import AWS from 'aws-sdk';

AWS.config.update({
  httpOptions: {
    timeout: 30000 // 30 seconds
  }
});
// Initialize AWS SES with the configuration
const awsConfig = {
  region: process.env.AWS_SES_REGION || "us-east-1",
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
};


const ses = new AWS.SES(awsConfig);
const emailSchema = [
  { field: 'recipientEmail', required: true, pattern: 'email' },
  { field: 'subject', required: true, pattern: 'string' },
  { field: 'htmlBody', required: true, pattern: 'string' },
  { field: 'senderEmail', required: false, pattern: 'email' }
];


// Utility function to validate email schema
function checkFormSchema(schema) {
  return (req, res, next) => {
    for (const field of schema) {
      const value = req.body[field.field];
      if (field.required && !value) {
        return res.status(400).json({ error: `${field.field} is required.` });
      }
      
      // Check pattern for field if provided
      if (field.pattern === 'email' && value && !validateEmail(value)) {
        return res.status(400).json({ error: `${field.field} must be a valid email.` });
      }
      
      if (field.pattern === 'string' && value && typeof value !== 'string') {
        return res.status(400).json({ error: `${field.field} must be a string.` });
      }
    }
    next(); // Proceed to the next middleware or route handler
  };
}

// Email validation function
function validateEmail(email) {
  const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return re.test(email);
}


// Function to send email using SES
async function sendEmail(recipientEmail, subject, htmlBody, senderEmail = null) {
  try {
    const params = {
      Destination: {
        ToAddresses: [recipientEmail], 
      },
      Source: senderEmail || process.env.SES_FROM_EMAIL || '<EMAIL>',
      Message: {
        Subject: {
          Data:subject,  
        },
        Body: {
          Html: {
            Data: htmlBody, 
          },
        },
      },
    };

    const result = await ses.sendEmail(params).promise();
    console.log("Email sent successfully:", result);
    return result;
  } catch (error) {
    console.error("Error sending email:", error);
    throw error;
  }
}

export default require('express').Router()

// API endpoint to send email with HTML content
.post('/send', checkFormSchema(emailSchema), async (req, res) => {
  const { recipientEmail, subject, htmlBody, senderEmail } = req.body;

  try {
    // Validate AWS SES configuration
    if (!process.env.AWS_ACCESS_KEY_ID || !process.env.AWS_SECRET_ACCESS_KEY) {
      return res.status(500).json({
        error: true,
        status: 'EMAIL::CONFIG_ERROR',
        message: 'AWS SES credentials not configured'
      });
    }

    const result = await sendEmail(recipientEmail, subject, htmlBody, senderEmail);
    res.status(200).json({
      error: false,
      status: 'EMAIL::SENT',
      message: `Email sent successfully to ${recipientEmail}`,
      messageId: result.MessageId
    });
  } catch (error) {
    res.status(500).json({
      error: true,
      status: 'EMAIL::SEND_ERROR',
      message: 'Error sending email',
      details: error.message
    });
  }
})


