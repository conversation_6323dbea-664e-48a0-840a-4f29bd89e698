
import BodyParts from '../../../src/data/bodyParts.json'
import Questionnaires from '../../../src/data/questionnaires.json'

export default require('express').Router()

// Retreive questions & reference by body part
.get('/reference', async ( req, res ) => {

  const { body_part } = req.query
  if( !body_part )
    return res.status(400)
              .json({
                error: true,
                status: 'REFERENCE::INVALID_REQUEST',
                message: 'Undefined Body Part'
              })

  if( !BodyParts.map( ({ value }) => { return value } ).includes( body_part ) )
    return res.status(400)
              .json({
                error: true,
                status: 'REFERENCE::INVALID_REQUEST',
                message: 'Unknown Body Part'
              })

  const results = []
  Object.entries( Questionnaires )
        .map( ([ reference, value ]) => {
          if( value.parts.includes( body_part ) )
            results.push({ reference, question: value.question })
        } )

  res.json({
    error: false,
    status: 'REFERENCE::QUESTIONS',
    results
  })
} )