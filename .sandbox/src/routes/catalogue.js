
export default require('express').Router()

.post('/add', async ( req, res ) => {
  const
  catalogue = req.body,
  attribute = catalogue.attribute
  
  await req.dp.catalogue.updateOne({ attribute }, catalogue, { upsert: true })

  res.json({
    error: false,
    status: 'CATALOGUE::ADD',
    message: 'Catalogue added'
  })
} )

.patch('/:attribute', async ( req, res ) => {
  const
   attribute  = req.params.attribute,
  body = req.body,
  catalogue = await req.dp.catalogue.updateOne({ attribute },  body , { upsert: true })

  delete catalogue._id

  res.json({
    error: false,
    status: 'CATALOGUE::UPDATED',
    message: 'Catalogue updated',
    catalogue
  })
} )

.get('/', async ( req, res ) => {
  const results = await req.dp.catalogue.find( {}, { desc: true, excludes: ['_id'] })

  res.json({
    error: false,
    status: 'CATALOGUE::FETCHED',
    results
  })
} )

.get('/:attribute', async ( req, res ) => {
  const
  { attribute } = req.params,
  catalogue = await req.dp.catalogue.findOne({ attribute }, { excludes: ['_id'] })
  if( !catalogue )
    return res.status(404)
              .json({
                error: true,
                status: 'CATALOGUE::NOT_FOUND',
                message: 'Catalogue Not Found'
              })

  delete catalogue._id

  res.json({
    error: false,
    status: 'CATALOGUE::RETRIEVED',
    catalogue
  })
} )

.delete('/:attribute', async ( req, res ) => {
  const
  { attribute } = req.params,
  catalogue = await req.dp.catalogue.deleteOne({ attribute })
  if( !catalogue )
    return res.status(404)
              .json({
                error: true,
                status: 'CATALOGUE::NOT_FOUND',
                message: 'Catalogue Not Found'
              })

  res.json({
    error: false,
    status: 'CATALOGUE::DELETED',
    message: 'Catalogue deleted'
  })
} )