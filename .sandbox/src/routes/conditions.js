import Diagnosis from '../../../src/data/diagnosis.json'

function getDeepValue( obj, key ){
  if( !typeof obj === 'object' ) null

  obj = JSON.parse( JSON.stringify( obj ) )

  const [ next, ...rest ] = key.split('.')
  if( !next ) return null

  if( Array.isArray( rest ) && rest.length )
    return getDeepValue( obj[ next ], rest.join('.') )

  return obj[ next ]
}
function includeObject( array, item, matches ){
  return array.filter( each => {
    if( typeof each !== 'object' || typeof item !== 'object' ) 
      return false
    
    for( const x in matches ){
      const
      a = getDeepValue( each, matches[x] ),
      b = getDeepValue( item, matches[x] )

      if( !a || !b || a !== b ) return false
    }

    return true
  } ).length > 0
}

const 
MINIMUM_AMOUNT_LOW_RATE = 1,
REVIEW_APPROVAL_RATE = 3,
REVIEW_DISAPPROVAL_RATE = -3

function byReview( status, by ){
  // TODO: Apply rate base on only allowed physicians review: <PERSON>

  return status == 'APPROVED' ? REVIEW_APPROVAL_RATE : REVIEW_DISAPPROVAL_RATE
}

export default require('express').Router()

// Check suggestion conditions validity base on review surveys
.post('/validate', async ( req, res ) => {
  const 
  { bests, alternatives, path } = req.body,
  stats = {},
  valid = [],
  suggestions = []

  if( ( Array.isArray( bests ) && bests.length )
      || ( Array.isArray( alternatives ) && alternatives.length ) ){
    const condition = {
      $and: [
        { $or: [{ 'bests.accuracy': { $exists: true } }, { 'alternatives.accuracy': { $exists: true } }] }
      ]
    }

    bests.map( each => condition.$and.push({ 'bests.attribute': each }) )
    alternatives.map( each => condition.$and.push({ 'alternatives.attribute': each }) )

    const records = await req.dp.surveys.find( condition )
    if( records.length ){
      const conditions = [ ...bests, ...alternatives ]
      
      function _rate( attribute ){
        if( !stats[ attribute ] )
          stats[ attribute ] = { high: 0, medium: 0, low: 0 }
          
        records.map( record => {
          const merged = [ ...record.bests, ...record.alternatives ]
          
          // Check and validate bests and alternative records
          merged.length && merged.map( each => {
            Array.isArray( each.accuracy )
            && each.accuracy.length
            && each.accuracy.map( ({ value, review, by }) => {
              if( each.attribute !== attribute ) return

              if( value == 'high' ){
                stats[ attribute ].high++
                if( review ) stats[ attribute ].high += byReview( review.status, by )
              }

              if( value == 'medium' ){
                stats[ attribute ].medium++
                if( review ) stats[ attribute ].medium += byReview( review.status, by )
              }

              if( value == 'low'){
                stats[ attribute ].low++
                if( review ) stats[ attribute ].low += byReview( review.status, by )
              }
            })
          } )
          
          // Add suggested conditions
          // record.suggestions && record.suggestions.map( ({ condition, review }) => {
          //   review
          //   && review.status == 'APPROVED'
          //   && !suggestions.includes( condition.attribute )
          //   && suggestions.push( condition.attribute )
          // } )
        })
      }

      function _validate( attribute ){
        if( !stats[ attribute ] ) return
        
        ( !stats[ attribute ].low
          || stats[ attribute ].low <= ( stats[ attribute ].high || stats[ attribute ].medium )
          || stats[ attribute ].low < MINIMUM_AMOUNT_LOW_RATE )
        && valid.push( attribute )
      }

      conditions.map( _rate )
      conditions.map( _validate )
    }
  }

  if( !Array.isArray( path ) || !path.length )
    return res.status(400)
              .send({
                error: true,
                status: 'CONDITION::INVALID_REQUEST',
                message: 'Invalid Request Body'
              })
              
  // Fetch possible suggestions base on path
  const sugCondition = {
    'suggestions.condition': { $exists: true },
    $and: []
  }

  path.map( ({ reference, answer }) => sugCondition.$and.push({ 'path.reference': reference, 'path.answer.key': answer.key }) )

  const sugRecords = await req.dp.surveys.find( sugCondition )
  if( !sugRecords.length )
    return res.send({
      error: true,
      status: 'CONDITION::NO_RECORD',
      message: 'No review record found'
    })
    
  sugRecords.map( record => {
    record.suggestions && record.suggestions.map( ({ condition, review }) => {
      review
      && review.status == 'APPROVED'
      && !suggestions.includes( condition.attribute )
      && suggestions.push( condition.attribute )
    } )
  } )
  
  res.json({
    error: false,
    status: 'CONDITION::VALIDATION',
    results: { stats, valid, suggestions }
  })
} )

// Search diagnosis/conditions
.get('/search', async ( req, res ) => {
  const
  { q, with_path } = req.query,
  results = []

  if( q.length >= 4 ){
    const regex = new RegExp( q, 'i' )

    Object.entries( Diagnosis )
          .map( ([ part, list ]) => {
            list.map( condition => {
              if( ( regex.test( condition.name )
                    || regex.test( condition.attribute ) )
                    && !includeObject( results, condition, ['attribute'] ) ){
                if( !with_path || String( with_path ) == 'false' ) 
                  delete condition.path

                results.push( condition )
              }
            })
          })
  }

  res.json({
    error: false,
    status: 'CONDITION::SEARCH',
    results
  })
} )
