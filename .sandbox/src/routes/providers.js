import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';
import { checkFormSchema } from '../lib/Validator';
import { requireA<PERSON><PERSON><PERSON>, requireProvider, requireProviderRole } from '../middleware/auth.js';

// Define schemas for provider operations
const providerLoginSchema = [
  { field: 'email', required: true, pattern: 'email' },
  { field: 'password', required: true, pattern: 'string' }
];

const providerCreateSchema = [
  { field: 'name', required: true, pattern: 'string' },
  { field: 'email', required: true, pattern: 'email' },
  { field: 'password', required: true, pattern: 'string' },
  { field: 'role', required: true, pattern: 'string' },
  { field: 'permissions', required: false, pattern: 'array' }
];

// Valid roles and permissions
const VALID_ROLES = ['admin', 'doctor', 'nurse', 'staff', 'viewer'];
const VALID_PERMISSIONS = [
  'read_surveys', 'write_surveys', 'delete_surveys',
  'read_questions', 'write_questions', 'delete_questions',
  'read_providers', 'write_providers', 'delete_providers',
  'read_tenants', 'write_tenants', 'delete_tenants',
  'send_emails', 'view_analytics'
];

// Helper function to generate authentication token
function generateAuthToken(provider) {
  const tokenData = {
    providerId: provider.providerId,
    email: provider.email,
    role: provider.role,
    timestamp: Date.now()
  };
  
  // Simple base64 encoding (use JWT in production)
  return Buffer.from(JSON.stringify(tokenData)).toString('base64');
}

export default require('express').Router()

// Provider login endpoint
.post('/login', checkFormSchema(providerLoginSchema), async (req, res) => {
  const { email, password } = req.body;

  try {
    // Find provider by email
    const provider = await req.dp.providers.findOne({ 
      email: email.toLowerCase(),
      'status.active': true 
    });

    if (!provider) {
      return res.status(401).json({
        error: true,
        status: 'AUTH::INVALID_CREDENTIALS',
        message: 'Invalid email or password'
      });
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, provider.password);
    if (!isValidPassword) {
      return res.status(401).json({
        error: true,
        status: 'AUTH::INVALID_CREDENTIALS',
        message: 'Invalid email or password'
      });
    }

    // Generate authentication token
    const token = generateAuthToken(provider);

    // Update last login
    await req.dp.providers.updateOne(
      { providerId: provider.providerId },
      { $set: { 'status.lastLogin': Date.now() } }
    );

    res.status(200).json({
      error: false,
      status: 'AUTH::LOGIN_SUCCESS',
      message: 'Login successful',
      token,
      provider: {
        id: provider.providerId,
        name: provider.name,
        email: provider.email,
        role: provider.role,
        permissions: provider.permissions || []
      }
    });
  } catch (error) {
    res.status(500).json({
      error: true,
      status: 'AUTH::LOGIN_ERROR',
      message: 'Login failed',
      details: error.message
    });
  }
})

// Create new provider (admin only)
.post('/create', requireApiKey, requireProviderRole(['admin']), checkFormSchema(providerCreateSchema), async (req, res) => {
  const { name, email, password, role, permissions = [] } = req.body;

  try {
    // Validate role
    if (!VALID_ROLES.includes(role)) {
      return res.status(400).json({
        error: true,
        status: 'PROVIDER::INVALID_ROLE',
        message: `Invalid role. Must be one of: ${VALID_ROLES.join(', ')}`
      });
    }

    // Validate permissions
    const invalidPermissions = permissions.filter(p => !VALID_PERMISSIONS.includes(p));
    if (invalidPermissions.length > 0) {
      return res.status(400).json({
        error: true,
        status: 'PROVIDER::INVALID_PERMISSIONS',
        message: `Invalid permissions: ${invalidPermissions.join(', ')}`
      });
    }

    // Check if provider already exists
    const existingProvider = await req.dp.providers.findOne({ 
      email: email.toLowerCase() 
    });

    if (existingProvider) {
      return res.status(409).json({
        error: true,
        status: 'PROVIDER::ALREADY_EXISTS',
        message: 'Provider with this email already exists'
      });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create provider object
    const provider = {
      providerId: uuidv4(),
      name,
      email: email.toLowerCase(),
      password: hashedPassword,
      role,
      permissions,
      status: {
        active: true,
        createdAt: Date.now(),
        createdBy: req.provider.id,
        lastLogin: null
      }
    };

    // Insert provider
    const result = await req.dp.providers.insert(provider);

    res.status(201).json({
      error: false,
      status: 'PROVIDER::CREATED',
      message: 'Provider created successfully',
      provider: {
        id: provider.providerId,
        name: provider.name,
        email: provider.email,
        role: provider.role,
        permissions: provider.permissions
      }
    });
  } catch (error) {
    res.status(500).json({
      error: true,
      status: 'PROVIDER::CREATE_ERROR',
      message: 'Failed to create provider',
      details: error.message
    });
  }
})

// Get all providers (admin only)
.get('/', requireProvider, requireProviderRole(['admin']), async (req, res) => {
  try {
    const providers = await req.dp.providers.find(
      { 'status.active': true },
      { excludes: ['password'] }
    );

    res.status(200).json({
      error: false,
      status: 'PROVIDER::FETCHED',
      providers: providers.map(p => ({
        id: p.providerId,
        name: p.name,
        email: p.email,
        role: p.role,
        permissions: p.permissions || [],
        createdAt: p.status.createdAt,
        lastLogin: p.status.lastLogin
      }))
    });
  } catch (error) {
    res.status(500).json({
      error: true,
      status: 'PROVIDER::FETCH_ERROR',
      message: 'Failed to fetch providers',
      details: error.message
    });
  }
})

// Get current provider profile
.get('/profile', requireProvider, async (req, res) => {
  try {
    const provider = await req.dp.providers.findOne(
      { providerId: req.provider.id },
      { excludes: ['password'] }
    );

    if (!provider) {
      return res.status(404).json({
        error: true,
        status: 'PROVIDER::NOT_FOUND',
        message: 'Provider not found'
      });
    }

    res.status(200).json({
      error: false,
      status: 'PROVIDER::PROFILE_FETCHED',
      provider: {
        id: provider.providerId,
        name: provider.name,
        email: provider.email,
        role: provider.role,
        permissions: provider.permissions || [],
        createdAt: provider.status.createdAt,
        lastLogin: provider.status.lastLogin
      }
    });
  } catch (error) {
    res.status(500).json({
      error: true,
      status: 'PROVIDER::PROFILE_ERROR',
      message: 'Failed to fetch profile',
      details: error.message
    });
  }
})

// Change password
.post('/change-password', requireProvider, async (req, res) => {
  const { currentPassword, newPassword } = req.body;

  if (!currentPassword || !newPassword) {
    return res.status(400).json({
      error: true,
      status: 'PROVIDER::INVALID_REQUEST',
      message: 'Current password and new password are required'
    });
  }

  try {
    // Get current provider with password
    const provider = await req.dp.providers.findOne({
      providerId: req.provider.id
    });

    if (!provider) {
      return res.status(404).json({
        error: true,
        status: 'PROVIDER::NOT_FOUND',
        message: 'Provider not found'
      });
    }

    // Verify current password
    const isValidPassword = await bcrypt.compare(currentPassword, provider.password);
    if (!isValidPassword) {
      return res.status(401).json({
        error: true,
        status: 'PROVIDER::INVALID_PASSWORD',
        message: 'Current password is incorrect'
      });
    }

    // Hash new password
    const hashedNewPassword = await bcrypt.hash(newPassword, 12);

    // Update password
    await req.dp.providers.updateOne(
      { providerId: req.provider.id },
      {
        $set: {
          password: hashedNewPassword,
          'status.passwordChangedAt': Date.now()
        }
      }
    );

    res.status(200).json({
      error: false,
      status: 'PROVIDER::PASSWORD_CHANGED',
      message: 'Password changed successfully'
    });
  } catch (error) {
    res.status(500).json({
      error: true,
      status: 'PROVIDER::PASSWORD_CHANGE_ERROR',
      message: 'Failed to change password',
      details: error.message
    });
  }
})
