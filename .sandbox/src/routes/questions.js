
export default require('express').Router()

.post('/add', async ( req, res ) => {

  // TODO: Apply request body validation


  // Insert new question
  const body = req.body

  // Assign new reference by incrementing the last question reference
  if( !body.reference ){
    const
    list = await req.dp.staged_questions.find({}, { desc: true, limit: 1 }),
    reference = !list || !list.length ? 0 : parseInt( list[0].reference )

    // Increment
    body.reference = '0'+(reference + 1)

    Object
    .entries( body.answers )
    .map( ([ key, value ]) => {
      body.answers[`${body.reference}:${key.split(':')[1]}`] = value
      delete body.answers[ key ]
    })
  }

  const condition = {
    question: body.question,
    reference: body.reference
  }

  await req.dp.staged_questions.updateOne( condition, body, { upsert: true })

  res.json({
    error: false,
    status: 'QUESTIONS::ADD',
    message: 'Question added'
  })
} )

.get('/', async ( req, res ) => {
  const 
  { parts } = req.query,
  condition = parts ? { parts: { $elemMatch: { $in: parts.split(/\s*,\s*/) } } } : {},
  results = await req.dp.staged_questions.find( condition, { desc: true, excludes: ['_id'] })

  res.json({
    error: false,
    status: 'QUESTIONS::FETCHED',
    results
  })
} )

.get('/lastref', async ( req, res ) => {
  const questions = await req.dp.staged_questions.find({}, { desc: true, limit: 1 })

  res.json({
    error: false,
    status: 'QUESTIONS::RETRIEVED',
    reference: !questions || !questions.length ? 0 : questions[0].reference
  })
} )

.patch('/:reference', async ( req, res ) => {
  const body = req.body
  delete body._id

  const question = await req.dp.staged_questions.updateOne({ reference: req.params.reference }, { $set: body }, { returnUpdate: true })

  res.json({
    error: false,
    status: 'QUESTIONS::UPDATED',
    message: 'Question updated',
    question
  })
} )

.post('/link', async ( req, res ) => {
  const 
  { from, to } = req.body,
  condition = { [`answers.${from}`]: { $exists: true } },
  above = await req.dp.staged_questions.updateOne( condition, { $push: { [`answers.${from}.references`]: to } })
  if( !above )
    return res.status(404)
              .json({
                error: true,
                status: 'QUESTIONS::NOT_FOUND',
                message: 'Question Not Found'
              })
  
  res.json({
    error: false,
    status: 'QUESTIONS::LINKED',
    message: 'Question linked'
  })
} )

.post('/relink', async ( req, res ) => {
  const 
  { from, to, current } = req.body,
  condition = { [`answers.${from}.references`]: current },
  above = await req.dp.staged_questions.updateOne( condition, { $set: { [`answers.${from}.references.$`]: to } })
  console.log( above )
  if( !above )
    return res.status(404)
              .json({
                error: true,
                status: 'QUESTIONS::NOT_FOUND',
                message: 'Question Not Found'
              })
  
  res.json({
    error: false,
    status: 'QUESTIONS::RELINKED',
    message: 'Question link altered'
  })
} )

.post('/unlink', async ( req, res ) => {
  const
  questions = req.body.filter( ({ key, reference }) => { return key || reference } ),
  links = req.body.filter( ({ fromPort, to }) => { return fromPort && to } )

  // Unlink
  await Promise.all( links.map( async ({ fromPort, to }) => {
    await req.dp.staged_questions.updateOne({ [`answers.${fromPort}.references`]: to }, { $pull: { [`answers.${fromPort}.references`]: to } })
  } ) )

  // Delete related questions
  await Promise.all( questions.map( async ({ key, reference }) => {
    await req.dp.staged_questions.deleteOne({ reference: key || reference })
  } ) )

  res.json({
    error: false,
    status: 'QUESTIONS::UNLINKED',
    message: 'Questions unlinked'
  })
} )

.get('/:query', async ( req, res ) => {
  const 
  condition = { $or: [{ reference: req.params.query }, { question: req.params.query }] },
  question = await req.dp.staged_questions.findOne( condition )
  if( !question )
    return res.status(404)
              .json({
                error: true,
                status: 'QUESTIONS::NOT_FOUND',
                message: 'Question Not Found'
              })

  res.json({
    error: false,
    status: 'QUESTIONS::RETRIEVED',
    question
  })
} )

.delete('/:reference', async ( req, res ) => {
  const { reference } = req.params

  // Remove any parent question linked to this
  await req.dp.staged_questions.updateOne({ [`answers.$.references`]: reference }, { $pull: { [`answers.$.references`]: reference } })

  // Delete question
  const question = await req.dp.staged_questions.deleteOne({ reference })
  if( !question )
    return res.status(404)
              .json({
                error: true,
                status: 'QUESTIONS::NOT_FOUND',
                message: 'Question Not Found'
              })

  res.json({
    error: false,
    status: 'QUESTIONS::DELETED',
    message: 'Question deleted'
  })
} )