
export default require('express').Router()

.get('/question/:query', async ( req, res ) => {
  const 
  condition = { $or: [{ reference: req.params.query }, { question: req.params.query }] },
  question = await req.dp.questions.findOne( condition )
  if( !question )
    return res.status(404)
              .json({
                error: true,
                status: 'QUESTIONS::NOT_FOUND',
                message: 'Question Not Found'
              })

  res.json({
    error: false,
    status: 'QUESTIONS::RETRIEVED',
    question
  })
})

.get('/diagnosis/:query', async ( req, res ) => {
  const 
  condition = { $or: [{ name: req.params.query }, { attribute: req.params.query }] },
  diagnosis = await req.dp.diagnosis.findOne( condition )
  if( !diagnosis )
    return res.status(404)
              .json({
                error: true,
                status: 'DIAGNOSIS::NOT_FOUND',
                message: 'Diagnosis Not Found'
              })

  res.json({
    error: false,
    status: 'DIAGNOSIS::RETRIEVED',
    diagnosis
  })
})

.get('/diagnosis', async ( req, res ) => {
  const
  { parts } = req.query,
  condition = parts ? { parts: { $elemMatch: { $in: parts.split(/\s*,\s*/) } } } : {},
  results = await req.dp.diagnosis.find( condition, { desc: true, excludes: ['_id'] })

  res.json({
    error: false,
    status: 'DIAGNOSIS::FETCHED',
    results
  })
})