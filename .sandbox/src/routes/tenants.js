import moment from 'moment'
import { ObjectId, MongoClient } from 'mongodb'
import { v4 as uuidv4 } from 'uuid';
export default require('express').Router()




// Fetch tenants
.get('/', async ( req, res ) => {
  const
  limit = Number( req.query.limit ) || 50,
  operators = { limit, desc: true, excludes: ['_id'] },
  condition = {}

  if( req.query.tenant_name )
    condition.tenant_name = { $elemMatch: { $in: req.query.tenant_name.split(/\s*,\s*/) } }

  // Filter by status (defaults to true if not specified)
  const status = req.query.status !== undefined ? req.query.status === 'true' : true
  condition['config.status'] = status

  const
  results = await req.dp.eligible_tenants.find( condition, operators ),
  response = {
    error: false,
    status: 'TENANTS::FETCHED',
    results
  }

  // Return URL to be call to get more results
  if( results.length == limit )
    response.more = `/?offset=${results[ limit - 1 ].saved.at}&limit=${limit}`

  return res.json( response )
} )

// Search surveys
.get('/search', async ( req, res ) => {
  const
  { q, fts } = req.query,
  operators = { desc: true, excludes: ['_id'] },
  matcher = { $regex: String( q ), $options: 'i' },
  condition = { $or: [] }
  
  // Narrow survey to a given tenant's entries
  if( req.headers['x-tenant-id'] ){
    condition.tenantId = req.headers['x-tenant-id']
    
    condition.$or.push({ 'patient.name': matcher })
    condition.$or.push({ 'patient.dob': matcher })
  }

  if( fts )
    try {
      const
      { part, physician, from, to, reviewed } = JSON.parse( fts ),
      $and = []

      if( part ) $and.push({ 'parts': { $elemMatch: { $eq: part } } })
      if( physician ){
        const matchPhysician = { $elemMatch: { $or: [{ by: physician }, { 'review.by': physician }] } }
        $and.push({
          $or: [
            { 'physician.name': physician },
            { 'bests': { $elemMatch: { accuracy: matchPhysician } } },
            { 'alternatives': { $elemMatch: { accuracy: matchPhysician } } },
            { 'suggestions': matchPhysician }
          ]
        })
      }

      if( reviewed )
        $and.push({
          $or: [
            { 'bests.accuracy': { $exists: true } },
            { 'alternatives.accuracy': { $exists: true } },
            { 'suggestions.condition': { $exists: true } }
          ]
        })

      if( from || to ){
        const savedAt = {}

        if( from ) savedAt.$gte = Number( moment( from ).format('x') )
        if( to ) savedAt.$lte = Number( moment( to ).format('x') )

        $and.push({ 'saved.at': savedAt })
      }

      if( $and.length ) condition.$and = $and
    }
    catch( error ){ console.log('Parsing search filters Error: ', error ) }

  condition.$or.push({ 'physician.name': matcher })
  condition.$or.push({ 'path.question': matcher })
  condition.$or.push({ 'path.answer.value': matcher })

  condition.$or.push({ 'parts': { $elemMatch: matcher } })

  condition.$or.push({ 'bests': { $elemMatch: { name: matcher } } })
  condition.$or.push({ 'bests': { $elemMatch: { attribute: matcher } } })
  condition.$or.push({ 'bests': { $elemMatch: { 'content.title': matcher } } })
  condition.$or.push({ 'bests': { $elemMatch: { 'content.description': matcher } } })
  condition.$or.push({ 'bests': { $elemMatch: { accuracy: { $elemMatch: { $or: [{ value: matcher }, { by: matcher }, { 'review.by': matcher }, { 'review.status': matcher }] } } } } })

  condition.$or.push({ 'alternatives': { $elemMatch: { name: matcher } } })
  condition.$or.push({ 'alternatives': { $elemMatch: { attribute: matcher } } })
  condition.$or.push({ 'alternatives': { $elemMatch: { 'content.title': matcher } } })
  condition.$or.push({ 'alternatives': { $elemMatch: { 'content.description': matcher } } })
  condition.$or.push({ 'alternatives': { $elemMatch: { accuracy: { $elemMatch: { $or: [{ value: matcher }, { by: matcher }, { 'review.by': matcher }, { 'review.status': matcher }] } } } } })

  condition.$or.push({ 'suggestions': { $elemMatch: { 'condition.name': matcher } } })
  condition.$or.push({ 'suggestions': { $elemMatch: { 'condition.attribute': matcher } } })
  condition.$or.push({ 'suggestions': { $elemMatch: { 'condition.content.title': matcher } } })
  condition.$or.push({ 'suggestions': { $elemMatch: { 'condition.content.description': matcher } } })
  condition.$or.push({ 'suggestions': { $elemMatch: { $or: [{ by: matcher }, { 'review.by': matcher }, { 'review.status': matcher }] } } })
  
  res.json({
    error: false,
    status: 'SURVEY::SEARCH',
    results: await req.dp.surveys.find( condition, operators )
  })
} )

// Rate survey accuracy by a physician
.patch('/:id/accuracy/:attr', async ( req, res ) => {
  const
  { id, attr } = req.params,
  condition = { _id: new ObjectId( id ) }
  
  if( !( await req.dp.surveys.findOne( condition ) ) )
    return res.status(404)
              .json({
                error: true,
                status: 'SURVEY::NOT_FOUND',
                message: 'Survey Not Found'
              })

  // As: `best` or `alternatives`
  const { as, value, by } = req.body

  // Remove previous accuracy rate added by this physician
  await req.dp.surveys.updateOne( condition, 
                                  { $pull: { [`${as}s.$[each].accuracy`]: { by } } },
                                  { arrayFilters: [{ 'each.attribute': attr }] })

  // Target bests or alternatives condition by attribute
  const
  toPush = { [`${as}s.$[each].accuracy`]: { value, by, at: Date.now() } },
  survey = await req.dp.surveys.updateOne( condition,
                                            { $push: toPush },
                                            {
                                              returnUpdate: true,
                                              arrayFilters: [{ 'each.attribute': attr }] 
                                            })

  res.json({
    error: false,
    status: 'SURVEY::UPDATED',
    message: 'Survey is updated',
    survey
  })
} )

// Physician suggest a best condition
.patch('/:id/suggest', async ( req, res ) => {
  const
  { id } = req.params,
  condition = { _id: new ObjectId( id ) }
  
  if( !( await req.dp.surveys.findOne( condition ) ) )
    return res.status(404)
              .json({
                error: true,
                status: 'SURVEY::NOT_FOUND',
                message: 'Survey Not Found'
              })

  const 
  suggestions = [],
  { conditions, by } = req.body

  conditions.map( each => {
    suggestions.push({ condition: each, by, at: Date.now() })
  } )

  const survey = await req.dp.surveys.updateOne( condition, 
                                                  { $addToSet: { suggestions: { $each: suggestions } } },
                                                  { returnUpdate: true })

  res.json({
    error: false,
    status: 'SURVEY::UPDATED',
    message: 'Survey is updated',
    survey
  })
} )

// Physician unsuggest a condition
.patch('/:id/unsuggest', async ( req, res ) => {
  const
  { id } = req.params,
  condition = { _id: new ObjectId( id ) }
  
  if( !( await req.dp.surveys.findOne( condition ) ) )
    return res.status(404)
              .json({
                error: true,
                status: 'SURVEY::NOT_FOUND',
                message: 'Survey Not Found'
              })

  const
  { attribute, by } = req.body,
  survey = await req.dp.surveys.updateOne( condition,
                                            { $pull: { suggestions: { 'condition.attribute': attribute } } },
                                            { returnUpdate: true })

  res.json({
    error: false,
    status: 'SURVEY::UPDATED',
    message: 'Survey is updated',
    survey
  })
} )

// Physician review a condition
.patch('/:id/review/:type', async ( req, res ) => {

  const { attribute, author, as, status, by } = req.body
  if( !status || !by || !attribute || !author )
    return res.status(400)
              .json({
                error: true,
                status: 'SURVEY::INVALID_REQUEST',
                message: 'Invalid Request Payload'
              })

  const
  { id } = req.params,
  condition = { _id: new ObjectId( id ) }
  if( !( await req.dp.surveys.findOne( condition ) ) )
    return res.status(404)
              .json({
                error: true,
                status: 'SURVEY::NOT_FOUND',
                message: 'Survey Not Found'
              })

  const { type } = req.params
  let survey

  switch( type ){
    // Review accuracy rates
    case 'accuracy': {
      if( !as )
        return res.status(400)
                  .json({
                    error: true,
                    status: 'SURVEY::INVALID_REQUEST',
                    message: 'Invalid Request Payload'
                  })

      condition[`${as}s.attribute`] = attribute
      condition[`${as}s.accuracy.by`] = author

      survey = await req.dp.surveys.updateOne( condition,
                                                { $set: { [`${as}s.$[condition].accuracy.$[each].review`]: { status, by, at: Date.now() } } },
                                                {
                                                  returnUpdate: true,
                                                  arrayFilters: [{ 'condition.attribute': attribute }, { 'each.by': author }]
                                                })
    } break

    // Review suggested conditions
    case 'suggestions': {
      condition['suggestions.condition.attribute'] = attribute
      condition['suggestions.by'] = author
      
      survey = await req.dp.surveys.updateOne( condition,
                                                { $set: { [`suggestions.$[each].review`]: { status, by, at: Date.now() } } },
                                                { 
                                                  returnUpdate: true,
                                                  arrayFilters: [{ 'each.condition.attribute': attribute }] 
                                                })
    } break

    default: return res.status(400)
                        .json({
                          error: true,
                          status: 'SURVEY::INVALID_REQUEST',
                          message: 'Unknown Review Type'
                        })
  }

  res.json({
    error: false,
    status: 'SURVEY::REVIEWED',
    message: 'Survey review applied',
    survey
  })
} )

// Retreive survey by id
.get('/:id', async ( req, res ) => {
  const 
  { id } = req.params,
  survey = await req.dp.surveys.findOne({ _id: new ObjectId( id ) })
  if( !survey )
    return res.status(404)
              .json({
                error: true,
                status: 'SURVEY::NOT_FOUND',
                message: 'Survey Not Found'
              })
  
  res.json({
    error: false,
    status: 'SURVEY::RETREIVED',
    survey
  })
} )

// Delete survey by id
.delete('/:id', async ( req, res ) => {
  const 
  { id } = req.params,
  result = await req.dp.surveys.deleteOne({ _id: new ObjectId( id ) })
  if( result !== 'Deleted' )
    return res.status(404)
              .json({
                error: true,
                status: 'SURVEY::NOT_FOUND',
                message: 'Survey Not Found'
              })
  
  res.json({
    error: false,
    status: 'SURVEY::DELETED',
    message: 'Survey is deleted'
  })
} )
