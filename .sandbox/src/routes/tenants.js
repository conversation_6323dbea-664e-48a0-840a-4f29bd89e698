import moment from 'moment'
import { ObjectId, MongoClient } from 'mongodb'
import { v4 as uuidv4 } from 'uuid';
export default require('express').Router()




// Fetch tenants
.get('/', async ( req, res ) => {
  const
  limit = Number( req.query.limit ) || 50,
  operators = { limit, desc: true, excludes: ['_id'] },
  condition = {}

  if( req.query.tenant_name )
    condition.tenant_name = { $elemMatch: { $in: req.query.tenant_name.split(/\s*,\s*/) } }

  // Filter by status (defaults to true if not specified)
  const status = req.query.status !== undefined ? req.query.status === 'true' : true
  condition['config.status'] = status

  const
  results = await req.dp.eligible_tenants.find( condition, operators ),
  response = {
    error: false,
    status: 'TENANTS::FETCHED',
    results
  }

  // Return URL to be call to get more results
  if( results.length == limit )
    response.more = `/?offset=${results[ limit - 1 ].saved.at}&limit=${limit}`

  return res.json( response )
} )

