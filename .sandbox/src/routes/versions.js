
export default require('express').Router()

.get('/:version/stage', async ( req, res ) => {
  const
  { version } = req.params,
  vdata = await req.dp.versions.findOne({ version: Number( version ) })
  if( !vdata )
    return res.status(404)
              .json({
                error: true,
                status: 'VERSION::NOT_FOUND',
                message: `Data version ${version} not found`
              })

  // Drop all staged questions & diagnosis
  await req.dp.staged_questions.deleteMany({})
  await req.dp.staged_diagnosis.deleteMany({})

  // Insert selected version's questions & diagnosis
  Array.isArray( vdata.qs )
  && vdata.qs.length
  && await req.dp.staged_questions.insert( vdata.qs )

  Array.isArray( vdata.ds )
  && vdata.ds.length
  && await req.dp.staged_diagnosis.insert( vdata.ds )
  
  res.json({
    error: false,
    status: 'VERSION::STAGED',
    message: 'Message staged'
  })
})

.get('/:version', async ( req, res ) => {
  const
  { version } = req.params,
  data = await req.dp.versions.findOne({ version: Number( version ) }, { excludes: ['_id', 'qs', 'ds'] })
  if( !data )
    return res.status(404)
              .json({
                error: true,
                status: 'VERSION::NOT_FOUND',
                message: `Data version ${version} not found`
              })

  delete data._id
  
  res.json({
    error: false,
    status: 'VERSION::RETRIEVED',
    data
  })
})

.get('/', async ( req, res ) => {
  const results = await req.dp.versions.find({})

  // Clear old backup
  await req.dp.versions_backup.deleteMany({})
  // Create backup versions collection
  results.forEach( async each => {
    req.dp.versions_backup.insert( each )
    
    // TEMP: Replace `part` {string} in diagnosis ds with `parts` {array}
    // if( Array.isArray( each.ds ) ){
    //   const ds = each.ds.map( value => {
    //     if( !value.parts ){
    //       value.parts = [ value.part ]
    //       delete value.part
    //     }

    //     return value
    //   } )

    //   await req.dp.versions.updateOne({ version: each.version }, { $set: { ds } })
    // }
  } )

  res.json({
    error: false,
    status: 'VERSION::LIST',
    versions: results.map( ({ version, isDraft, published }) => { return { version, isDraft, published } } )
  })
})

.post('/:version/draft', async ( req, res ) => {

  let { version } = req.params

  // Publish new version
  if( version === '*' ){
    const versions = await req.dp.versions.find({})

    if( !versions.length ) version = 1
    else {
      let latest = 1
      versions.map( each => {
        if( each.version > latest )
          latest = each.version
      } )

      version = latest + 1 // Increase of latest version
    }
  }
  // Publish existing version: Check whether exists
  else {
    const
    condition = { version: Number( version ), published: { $exists: false } },
    exists = await req.dp.versions.findOne( condition )
    if( !exists )
      return res.status(404)
                .json({
                  error: true,
                  status: 'VERSION::NOT_FOUND',
                  message: `Data version ${version} not found`
                })
  }

  let
  qs = await req.dp.staged_questions.find({}),
  ds = await req.dp.staged_diagnosis.find({})

  qs = qs.map( each => {
    delete each._id
    return each
  })
  ds = ds.map( each => {
    delete each._id
    return each
  })

  version = Number( version )
  
  const
  { author } = req.body,
  vset = {
    version,
    isDraft: true,
    created: {
      by: author,
      at: Date.now()
    },
    qs,
    ds
  }
  
  await req.dp.versions.updateOne({ version }, { $set: vset }, { upsert: true })

  res.json({
    error: false,
    status: 'VERSION::SAVED',
    message: 'Draft saved'
  })
})

.post('/:version/publish', async ( req, res ) => {

  let { version } = req.params

  // Publish new version
  if( version === '*' ){
    const versions = await req.dp.versions.find({})

    if( !versions.length ) version = 1
    else {
      let latest = 1
      versions.map( each => {
        if( each.version > latest )
          latest = each.version
      } )

      version = latest + 1 // Increase of latest version
    }
  }
  // Publish existing version: Check whether exists
  else {
    version = Number( version )

    const exists = await req.dp.versions.findOne({ version })
    if( !exists )
      return res.status(404)
                .json({
                  error: true,
                  status: 'VERSION::NOT_FOUND',
                  message: `Data version ${version} not found`
                })
  }

  // Unpublished any published version.
  await req.dp.versions.updateMany({ published: { $exists: true } }, { $unset: { published: true } })

  let
  qs = await req.dp.staged_questions.find({}),
  ds = await req.dp.staged_diagnosis.find({})

  qs = qs.map( each => {
    delete each._id
    return each
  })
  ds = ds.map( each => {
    delete each._id
    return each
  })

  const
  { author } = req.body,
  vset = {
    version,
    isDraft: false,
    published: {
      by: author,
      at: Date.now()
    },
    qs,
    ds
  }
  
  await req.dp.versions.updateOne({ version }, { $set: vset }, { upsert: true })

  // Clear all staged questions & diagnosis
  await req.dp.questions.deleteMany({})
  await req.dp.diagnosis.deleteMany({})

  // Replace public data with published data
  Array.isArray( qs )
  && qs.length
  && await req.dp.questions.insert( qs )

  Array.isArray( ds )
  && ds.length
  && await req.dp.diagnosis.insert( ds )
  
  res.json({
    error: false,
    status: 'VERSION::PUBLISHED',
    message: 'Version published'
  })
})

.delete('/:version', async ( req, res ) => {
  const
  { version } = req.params,
  condition = { version: Number( version ) },
  exists = await req.dp.versions.findOne( condition )
  if( !exists )
    return res.status(404)
              .json({
                error: true,
                status: 'VERSION::NOT_FOUND',
                message: `Data version ${version} not found`
              })

  // Delete the version
  await req.dp.versions.deleteOne( condition )

  res.json({
    error: false,
    status: 'VERSION::DELETED',
    message: 'Version deleted'
  })
})