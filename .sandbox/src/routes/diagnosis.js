
export default require('express').Router()

.post('/add', async ( req, res ) => {

  // TODO: Apply request body validation


  // Insert new diagnosis
  const body = req.body
  if( !body.path ) body.path = []

  const condition = {
    name: body.name,
    attribute: body.attribute
  }

  await req.dp.staged_diagnosis.updateOne( condition, body, { upsert: true })

  res.json({
    error: false,
    status: 'DIAGNOSIS::ADD',
    message: 'Diagnosis added'
  })
} )

.patch('/update', async ( req, res ) => {

  if( !req.query.name )
    return res.status(400)
              .json({
                error: true,
                status: 'DIAGNOSIS::NOT_FOUND',
                message: 'Undefined Diagnosis <name> Parameter'
              })

  const body = req.body
  delete body._id

  if( Array.isArray( body.path ) && !body.path.length )
    delete body.path

  const diagnosis = await req.dp.staged_diagnosis.updateOne({ name: req.query.name }, { $set: body }, { returnUpdate: true })

  res.json({
    error: false,
    status: 'DIAGNOSIS::UPDATED',
    message: 'Diagnosis updated',
    diagnosis
  })
} )

.patch('/:attribute/path', async ( req, res ) => {

  const condition = { attribute: req.params.attribute }
  let diagnosis = await req.dp.staged_diagnosis.findOne( condition )
  if( !diagnosis )
    return res.status(404)
              .json({
                error: true,
                status: 'DIAGNOSIS::NOT_FOUND',
                message: 'Diagnosis Not Found'
              })
  
  let
  toPush = [],
  toPull = []

  req.body.map( ({ reference, action }) => {
    switch( action ){
      case 'add': !diagnosis.path.includes( reference ) && toPush.push( reference ); break
      case 'remove': diagnosis.path.includes( reference ) && toPull.push( reference ); break
    }
  } )
  
  if( toPush.length )
    diagnosis = await req.dp.staged_diagnosis.updateOne( condition, { $push: { path: { $each: toPush } } }, { returnUpdate: true } )

  if( toPull.length )
    diagnosis = await req.dp.staged_diagnosis.updateOne( condition, { $pull: { path: { $in: toPull } } }, { returnUpdate: true } )

  res.json({
    error: false,
    status: 'DIAGNOSIS::UPDATED',
    message: 'Diagnosis path updated',
    diagnosis
  })
} )

.get('/', async ( req, res ) => {
  const
  { parts } = req.query,
  condition = parts ? { parts: { $elemMatch: { $in: parts.split(/\s*,\s*/) } } } : {},
  results = await req.dp.staged_diagnosis.find( condition, { desc: true, excludes: ['_id'] })

  res.json({
    error: false,
    status: 'DIAGNOSIS::FETCHED',
    results
  })
} )

.get('/:query', async ( req, res ) => {
  const
  condition = { $or: [{ name: req.params.query }, { attribute: req.params.query }] },
  diagnosis = await req.dp.staged_diagnosis.findOne( condition )
  if( !diagnosis )
    return res.status(404)
              .json({
                error: true,
                status: 'DIAGNOSIS::NOT_FOUND',
                message: 'Diagnosis Not Found'
              })

  res.json({
    error: false,
    status: 'DIAGNOSIS::RETRIEVED',
    diagnosis
  })
} )

.delete('/:attribute/:part', async ( req, res ) => {
  const
  { part, attribute } = req.params,
  condition = { attribute, parts: { $elemMatch: { $eq: part } } },
  diagnosis = await req.dp.staged_diagnosis.deleteOne( condition )
  if( !diagnosis )
    return res.status(404)
              .json({
                error: true,
                status: 'DIAGNOSIS::NOT_FOUND',
                message: 'Diagnosis Not Found'
              })

  res.json({
    error: false,
    status: 'DIAGNOSIS::DELETED',
    message: 'Diagnosis deleted'
  })
} )