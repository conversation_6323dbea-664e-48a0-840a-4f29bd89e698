
import path from 'path'
import express from 'express'
import { MDP } from 'globe-sdk'
import markoMiddleware from '@marko/express'
import Entrypoint from './views/www.marko'
import PurgeDB from './lib/purgedb'

const Assets = require( process.env.RAZZLE_ASSETS_MANIFEST )
const app = express()

;( async () => {
  app
  .disable('x-powered-by')
  .use( markoMiddleware() ) // Enable res.marko
  .use( express.static( path.resolve( process.cwd() +'/.sandbox/', process.env.RAZZLE_PUBLIC_DIR ) ) )
  .use( express.json() ) // {limit: '50mb', extended: true}
  .use( express.urlencoded({ extended: true }) )

  /*---------------------------------------------------------------------------*/
  /** Initialize the Data-Provider Interface (Driver)
    for by-tenant database requests

    Gets prelist of entities/tables (Array) subject of query
    transactions and connection configurations
  */
  console.log('Database URI: ', process.env.DATABASE_URL )
  const mdp = await MDP.config( 'DB', {
                                        dbServer: process.env.DATABASE_URL,
                                        dbName: process.env.DATABASE_NAME,
                                        collections: [ 
                                          'questions', 'diagnosis', 'catalogue',
                                          'surveys', 'conditions',
                                          'versions', 'versions_backup',
                                          'staged_questions', 'staged_diagnosis','eligible_tenants' ]
                                      })

  /*---------------------------------------------------------------------------*/
  // Purge database with JSON data
  PurgeDB( mdp.dp() )

  /*---------------------------------------------------------------------------*/
  // Attach data provider interface to express
  app.use( mdp.express )

  /*---------------------------------------------------------------------------*/
  // Operation routes
  .use( '/', require('./routes').default )
  .use( '/surveys', require('./routes/surveys').default )
  .use( '/versions', require('./routes/versions').default )
  .use( '/questions', require('./routes/questions').default )
  .use( '/assessment', require('./routes/assessment').default )
  .use( '/diagnosis', require('./routes/diagnosis').default )
  .use( '/catalogue', require('./routes/catalogue').default )
  .use( '/conditions', require('./routes/conditions').default )
  .use('/tenants',require('./routes/tenants').default)
  .use('/email',require('./routes/email').default)

  .get('/*', (req, res) => {
    const scope = {
      title: 'Symptom Checker',
      Assets
    }

    res.marko( Entrypoint, scope )
  })

  /*------------------------ error handlers ------------------------*/
  // catch 404 and forward to error handler
  .use( ( req, res, next ) => {
    let error = new Error('Not Found')

    error.status = 404
    next( error )
  } )
  // Print error stacktrace at the backend and
  // render the related error page at the frontend
  .use( ( error, req, res, next ) => {
    const statusCode = error.status || 500

    // no stacktraces leaked to user in production mode
    if( process.env.NODE_ENV === 'development' )
      console.error('[ERROR] ', error )

    return res.status( statusCode ).json({ error: true, message: error.message })
  } )
} )()

export default app;
