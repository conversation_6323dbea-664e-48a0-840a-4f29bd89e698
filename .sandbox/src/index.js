import dotenv from 'dotenv'
import express from 'express'
import path from 'path'

// Load environment variables based on NODE_ENV
const envFile = process.env.NODE_ENV === 'production' ? '.env.production' : '.env.dev'
const envPath = path.resolve(__dirname, '../../', envFile)

// Try to load the environment-specific file first, then fallback to .env
dotenv.config({ path: envPath })
dotenv.config() // Fallback to .env if environment-specific file doesn't exist

let app = require('./server').default;

if (module.hot) {
  module.hot.accept('./server', function() {
    console.log('🔁  HMR Reloading `./server`...');
    try {
      app = require('./server').default;
    } catch (error) {
      console.error(error);
    }
  });
  console.info('✅  Server-side HMR Enabled!');
}

const port = Number( process.env.HTTP_PORT ) || 5000;

export default express()
  .use((req, res) => app.handle(req, res))
  .listen(port, function(err) {
    if (err) {
      console.error(err);
      return;
    }
    console.log(`> Started on port - ${port}`);
  });
