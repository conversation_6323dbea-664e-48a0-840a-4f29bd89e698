
import Shell from './views/Shell.marko'
import Storage from 'all-localstorage'
import IOF from 'iframe.io'

 
let Request

function isEmbedded(){
	return window.frameElement
					|| window !== window.parent 
					|| window.self !== window.top
}

function createRequest({ origin, accessToken }){
	return async ( endpoint, options ) => {
		const payload = {
			method: 'GET',
			...options,
			origin,
			endpoint,
			accessToken
		},
		_options = {
			method: 'POST',
			headers: { 'Content-Type': 'application/json' },
			body: JSON.stringify( payload )
		}
		
		const response = await ( await window.fetch('/', _options ) ).json()
		if( response.error ) throw new Error( response.message )

		return response
	}
}

function getConfig( chn ){
	// Check whether accessToken is valid
	return new Promise( ( resolve, reject ) => {
		chn.once('authenticate', metadata => {
			if( !metadata ) return reject('Invalid Configuration')
			if( !metadata.accessToken ) return reject('Undefined Access Token')
      
			// Create request handler
			Request = createRequest( metadata )

			// Check access and get configuration
			Request('/access/config')
					.then( ({ error, message, config }) => error ? reject( message ) : resolve({ metadata, config }) )
					.catch( reject )
		})
	})
}
 

function init( scope = {} ){
  window.Store = new Storage({ prefix: 'bprint', encrypt: true })
  window.CLIENT_ID = scope.clientId || !['osc.upswinghealth.io', 'localhost:4000', 'osc.upswinghealth.com',"assess.upswinghealth.com"].includes( window.location.host ) ? window.location.host : false

  Shell.renderSync( scope ).prependTo( document.body )
}

;( async () => {
  if( isEmbedded() ){
    const chn = new IOF({ debug: false }).listen()
    
    try {
      // const { metadata, config } = await getConfig( chn )
      init({ chn, embed: true })
    }
    catch( error ){
      console.log( error )
      chn.emit('error', error )
    }
  }
  else init()
} )()