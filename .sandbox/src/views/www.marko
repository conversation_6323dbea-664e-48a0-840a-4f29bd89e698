<!doctype html>
<html lang=input.lang multipple>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui"/>
    <title>${input.title}</title>
    <!-- <script src="https://kit.fontawesome.com/4930695e9d.js" crossorigin="anonymous"></script> -->
    <if( input.Assets.client.css )>
      <for|href, index| of=input.Assets.client.css>
        <link key=index rel="stylesheet" href=href>
      </for>
    </if>
  </head>
  <body>
    <if( process.env.NODE_ENV === 'production' )><script src=input.Assets.client.js defer crossorigin></script></if>
    <else><script src=input.Assets.client.js defer></script></else>
  </body>
</html>
