
  // Field to hide by every DB query
  export const EXCLUDE_DBFOUND_FIELDS = { _id:0 }

  export const HTTP_ERROR_MESSAGES = {
      "400": `Bad Request. Check ${process.env.ERRORS_PAGE_URL}/api/bnd/400`,
      "401": `Access Denied. Check ${process.env.ERRORS_PAGE_URL}/api/bnd/401`,
      "403": `Forbidden Access. Check ${process.env.ERRORS_PAGE_URL}/api/bnd/403`
  }

  export const getContent = ( text, variables, scope ) => {

    return text.replace( /\{\{\s*(\w+)\s*\}\}/g, ( matche, asvar ) => {
      // Clean spaces
      asvar = asvar.replace(/\s+/g, '')
      return ( variables.includes( asvar ) && scope[ asvar ] ) || ''
    } )
  }
