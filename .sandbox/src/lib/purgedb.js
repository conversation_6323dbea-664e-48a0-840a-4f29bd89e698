
import Questions from '../../../src/data/questionnaires.json'
import Diagnosis from '../../../src/data/diagnosis.json'
import Catalogue from '../../../src/data/catalogue.json'

export default async dp => {
  // Create and purge questionnaires collection
  const 
  qs = Object.values( Questions ),
  qs_exists = await dp.questions.find({}, { limit: 1 })

  !qs_exists.length
  && qs.map( async each => {
      const condition = { reference: each.reference, question: each.question }
      await dp.questions.updateOne( condition, { $set: each }, { upsert: true })
    })

  // Create and purge diagnosis collection
  const
  ds = [],
  ds_exists = await dp.diagnosis.find({}, { limit: 1 })

  !ds_exists.length
  && Object.values( Diagnosis )
          .map( async section => {
            section.map( async each => {
              ds.push( each )

              const condition = { name: each.name, attribute: each.attribute }
              await dp.diagnosis.updateOne( condition, { $set: each }, { upsert: true })
            })
          })

  // Create and purge versions collection
  const vs_exists = await dp.versions.find({}, { limit: 1 })
  if( !vs_exists.length ){
    const
    condition = { version: 1 },
    vset = {
      version: 1,
      isDraft: true,
      created: {
        by: '--',
        at: Date.now()
      },
      // published: {
      //   by: '--',
      //   at: Date.now()
      // }
      qs,
      ds
    }

    await dp.versions.updateOne( condition, { $set: vset }, { upsert: true })
  }

  // Create/Update conditions catalogue
  const cat_exists = await dp.catalogue.find({}, { limit: 1 })
  
  !cat_exists.length
  && Object.keys( Catalogue ).length
  && Object.entries( Catalogue )
            .map( async ([ attribute, value ]) => {
              value.attribute = attribute
              await dp.catalogue.updateOne({ attribute }, { $set: value }, { upsert: true })
            })
}