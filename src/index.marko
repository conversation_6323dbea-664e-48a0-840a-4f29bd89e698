
import jQuery from 'jquery'
import './styles/index.scss'
import './styles/custom.scss'
import './styles/default/light.scss'

static window.$ = window.jQuery = jQuery

<if( document.location.origin.includes( window.CLIENT_ID ) )>
  <if( document.location.pathname.includes('admin') )>
    <Review/>
  </if>
  <else-if( document.location.pathname.includes('physician') )>
    <Physician/>
  </else-if>
  <else><Patient/></else>
</if>
<else>
  <if( document.location.pathname.includes('admin') )>
    <Admin/>
  </if>
  <else-if( document.location.pathname.includes('physician') )>
    <Physician/>
  </else-if>
  <else-if( document.location.pathname.includes('review') )>
    <Review/>
  </else-if>
  <else><Assessment ...input/></else>
</else>