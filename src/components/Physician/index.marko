
class {
  async saveAccuracy( id, as, attribute, value ){
    if( !this.physician || !id ) return
    try {
      const
      options = {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ as, value, by: this.physician.name })
      },
      { error, message } = await ( await window.fetch(`/surveys/${id}/accuracy/${attribute}`, options ) ).json()
      if( error ) throw new Error( message )
    }
    catch( error ){ console.log('Failed saving survey accuracy: ', error ) }
  }
  async saveSuggestions( id, conditions, callback ){
    if( !this.physician || !id ) return
    try {
      const
      options = {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ conditions, by: this.physician.name })
      },
      { error, message } = await ( await window.fetch(`/surveys/${id}/suggest`, options ) ).json()
      if( error ) throw new Error( message )
    }
    catch( error ){ console.log('Failed saving survey accuracy: ', error ) }

    typeof callback == 'function' && callback()
  }
}

<Session type="physician" on-auth( data => component.physician = data )/>
<Assessment physician
            on-accuracy('saveAccuracy')
            on-suggest('saveSuggestions')/>