// <!-- StyleSheets -->

import "boxicons/css/boxicons.min";
import "~/css/bootstrap";
import "~/css/bootstrap-extended";
import "~/css/colors.min";
import "~/css/components.min";
import "~/css/style.helper.min";
import "~/css/animate";
import { CsvBuilder } from "filefy";
import StartQuestions from "../../data/startQuestions.json";
import { toCapitalCase, getDeepValue, includeObject } from "../../utils";
static const MOST_LIKELY_VALIDATION_PERCENTAGE = 100;
static const LESS_LIKELY_VALIDATION_PERCENTAGE = 85;
static const INDICE_RATE = 36;
class {
  onCreate({ chn }) {
    this.state = {
      emergency: false,
      activePart: null,
      activeQuestion: null,
      diagnosis: null,
      searchResults: null,

      suggesting: false,
      fetching: false,
      loading: false,
      goPrev: false,
      more: null,
      readMore: false,
      selectedDiagnosis: null,
      test_results: null,
      tenantId: null,
      view_results: false,
      careJourneyId: null,
    };

    this.path = [];
    this.chn = chn || null;

    this.onShowStoredResult();
  }

  async onSelectPart(part) {
    const question = await this.getQuestion(StartQuestions[part].id);
    if (!question) return;

    // Only need when assessing one body part at a time
    this.path = [];

    this.setState({
      activePart: part,
      activeQuestion: question,
      diagnosis: null,
      emergency: false,
    });
  }
  onAnswer(record, nextRefs, multipleAnswer) {
    if (multipleAnswer) {
      includeObject(this.path, record, ["answer.key"])
        ? // Remove already added possible answer
          (this.path = this.path.filter((each) => {
            return (
              each.reference !== record.reference ||
              each.answer.key !== record.answer.key
            );
          }))
        : // Record new answer
          this.path.push(record);
    } else {
      !includeObject(this.path, record, ["answer.key"]) &&
        this.path.push(record);

      this.onNext(nextRefs);
    }
  }

  async onPrev() {
    if (!this.path.length) return;

    const { reference } = this.path.pop(),
      question = await this.getQuestion(reference);
    if (!question) return;

    this.state.activeQuestion = question;

    if (!this.path.length) this.state.goPrev = false;
  }
  async onNext(refs) {
    if (Array.isArray(refs) && refs.length) {
      const question = await this.getQuestion(refs[0]);
      if (!question) return;

      this.state.activeQuestion = question;
      this.state.goPrev = true;
    } else {
      this.state.goPrev = false;
      this.resolveDiagnose();
    }
  }

  onRestart() {
    this.path = []; // Clear previously recorded path
    this.setState({ activeQuestion: null, diagnosis: null });
    this.onSelectPart(this.state.activePart);
  }
  onDownload() {
    if (
      Array.isArray(this.state.diagnosis.bests) &&
      this.state.diagnosis.bests.length
    ) {
      const DReport = new CsvBuilder("condition-report.csv").setColumns([
        "Name",
        "Description",
        "Link",
      ]);

      this.state.diagnosis.bests.map(({ name, content, link }) =>
        DReport.addRow([name, content.description, link]),
      );
      DReport.exportFile();
    }

    if (
      Array.isArray(this.state.diagnosis.alternatives) &&
      this.state.diagnosis.alternatives.length
    ) {
      const AReport = new CsvBuilder("alternatives-report.csv").setColumns([
        "Name",
        "Description",
        "Link",
      ]);

      this.state.diagnosis.alternatives.map(({ name, content, link }) =>
        AReport.addRow([name, content.description, link]),
      );
      AReport.exportFile();
    }

    if (this.path.length) {
      const QReport = new CsvBuilder("questionnaire-report.csv").setColumns([
        "Question",
        "Answer",
      ]);

      this.path.map(({ question, answer }) =>
        QReport.addRow([question, answer.value]),
      );
      QReport.exportFile();
    }
  }
  async onShowStoredResult() {
    const urlParams = new URLSearchParams(document.location.search);
    const token = urlParams.get("token");
    const { path, activePart, view_results, careJourneyId } = JSON.parse(
      atob(token),
    );

    this.path = path;
    this.state.activePart = activePart;

    console.log(
      "care journey id :: ",
      careJourneyId,
      "view results :: ",
      view_results,
    );
    if (path && activePart) {
      const lastQuestionMD = path[path.length - 1];
      const questionRef = lastQuestionMD.reference;
      const question = await this.getQuestion(questionRef);
      this.state.activeQuestion = question;
      this.state.view_results = view_results;
      this.state.careJourneyId = careJourneyId;
      this.resolveDiagnose();
    }
  }

  onSeeCoach() {
    window.parent.postMessage("see-a-coach", "*");
    this.chn && this.chn.emit("connect");
  }

  async getQuestion(reference) {
    this.state.loading = true;
    try {
      const { error, message, question } = await (
        await window.fetch(`/assessment/question/${reference}`)
      ).json();
      if (error) throw new Error(message);

      this.state.loading = false;
      return question;
    } catch (error) {
      console.log("Failed getting question: ", error);
      this.state.loading = false;
      return null;
    }
  }
  async getDiagnosis(part) {
    this.state.loading = true;
    try {
      const { error, message, results } = await (
        await window.fetch(`/assessment/diagnosis?parts=${part}`)
      ).json();
      if (error) throw new Error(message);

      return results;
    } catch (error) {
      console.log("Failed fetching diagnosis: ", error);
      return null;
    }
  }

  async getCatalogue() {
    this.state.loading = true;
    try {
      const { error, message, results } = await (
        await window.fetch(`/catalogue`)
      ).json();
      if (error) throw new Error(message);

      // Convert cataloque array to object for better indexing
      const catalogueObject = {};
      results.map((each) => (catalogueObject[each.attribute] = each));

      return catalogueObject;
    } catch (error) {
      console.log("Failed fetching catalogue: ", error);
      return null;
    }
  }
  async resolveDiagnose() {
    // Check whether any of the first 2 questions are checked yes
    if (this.path.length <= 2) {
      this.state.emergency = true;
      return;
    }

    // Patient answer more than the two first (Emergency case) questions
    const diagnoses = [],
      DIAGNOSIS = await this.getDiagnosis(this.state.activePart),
      Catalogue = await this.getCatalogue();

    DIAGNOSIS.map(({ attribute, name, path }, index) => {
      diagnoses.push({ attribute, name, indice: 0, by: path.length });

      this.path.map(({ answer }) => {
        if (path.includes(answer.key)) diagnoses[index].indice++;
      });
    });

    let bests = [],
      alternatives = [];

    diagnoses.map(({ attribute, name, indice, by }) => {
      // console.log('Attribute: ', attribute, !!Catalogue[ attribute ] )

      if (!Catalogue[attribute] || !Catalogue[attribute].content) {
        console.log(`Condition -- ${attribute} Not found`);
        return;
      }

      const percentage = (indice / this.path.length) * 100,
        content = Catalogue[attribute].content,
        eachSet = {
          attribute,
          name,
          indice,
          percentage,
          rate: (indice / by) * 100,
          // Accuration validation of matches
          mostlikely: percentage >= MOST_LIKELY_VALIDATION_PERCENTAGE,
          lesslikely: percentage >= LESS_LIKELY_VALIDATION_PERCENTAGE,
          link: Catalogue[attribute].link,
          content,
        };
      // console.log( eachSet )

      if (eachSet.mostlikely && !includeObject(bests, eachSet, ["attribute"]))
        bests.push(eachSet);
      else if (
        eachSet.lesslikely &&
        !includeObject(alternatives, eachSet, ["attribute"])
      )
        alternatives.push(eachSet);
    });

    // Ignore alternatives if multiple best conditions are found
    if (bests.length) alternatives = [];

    // Apply accuracy review to conditions
    const payload = {
        path: this.path,
        bests: bests.map(({ attribute }) => {
          return attribute;
        }),
        alternatives: alternatives.map(({ attribute }) => {
          return attribute;
        }),
      },
      _AR = await this.resolveAccuracyValidity(payload);
    // console.log('Accuracy Reviews: ', _AR )

    if (_AR) {
      if (_AR.valid.length) {
        // Filter only validated bests
        bests = bests.filter(({ attribute }) => {
          return _AR.valid.includes(attribute);
        });

        // Record validated alternatives as bests
        alternatives.map(
          (each) => _AR.valid.includes(each.attribute) && bests.push(each),
        );
        alternatives = [];
      }

      // Add suggested conditions to bests conditions
      _AR.suggestions &&
        _AR.suggestions.length &&
        _AR.suggestions.map((attribute) => {
          const content = Catalogue[attribute].content;
          // console.log('Suggestions: ', attribute )

          bests.push({
            attribute,
            indice: null,
            rate: null,
            percentage: 100,
            mostlikely: true,
            lesslikely: false,
            link: Catalogue[attribute].link,
            content,
          });
        });
    }

    this.state.diagnosis = {
      path: this.path,
      diagnoses,
      bests,
      alternatives,
    };

    //todo : - don't save results if user is viewing it
    try {
      // Emit diagnosis result in embedding mode
      this.chn &&
        bests.length &&
        this.chn
          .emit("results", {
            // patientId: this.input.patientId,
            path: this.path,
            part: this.state.activePart,
            bests,
            alternatives,
          })
          .catch((err) => {
            console.log("failed to emit the results at resolveDiagnose");
          });
    } catch {
      console.log("failed to emit the results at resolveDiagnose");
    }

    // Case of active patient session
    let patient = null;
    let tenantId = null;
    let test_results = null;
    let careJourneyId = null;

    const urlParams = new URLSearchParams(document.location.search);
    const token = urlParams.get("token");

    console.log(token, "token");

    try {
      if (token) {
        const tokenDecode = JSON.parse(atob(token));
        const session = Store.get("session-patient");
        patient = {
          name: session.name,
          userId: tokenDecode.user,
          // dob: session.dob
        };
        tenantId = tokenDecode.host;
        test_results = tokenDecode.test_results;
        careJourneyId = tokenDecode.careJourneyId;

        this.state.tenantId = tenantId;
        this.state.test_results = test_results;
        this.state.careJourneyId = careJourneyId;

        console.log("care jpurney id ::", careJourneyId, tokenDecode);
      }
    } catch {
      // should we block anonymous users
    }

    if (this.input.patient || (token && token.user && token.user !== "")) {
      // if( !session || !session.name ){
      //  Store.clear('session-patient')
      //  window.location.reload()
      //  return
      // }
    }

    // Case of active physician session
    let physician = null;
    if (this.input.physician) {
      const session = Store.get("session-physician");

      // if( !session || !session.name ){
      //  Store.clear('session-physician')
      //  window.location.reload()
      //  return
      // }

      physician = {
        name: session.name,
      };
    }
    if (!this.state.view_results) {
      const urlParams = new URLSearchParams(document.location.search);
      const token = urlParams.get("token");
      const { careJourneyId } = JSON.parse(atob(token));

      console.log("care journey id on save results", careJourneyId);
      await this.save({
        isValidated: !!(_AR && _AR.valid && _AR.valid.length),
        tenantId: tenantId,
        careJourneyId: careJourneyId,
        patient,
        physician,
        parts: [this.state.activePart],
        path: this.path,
        bests,
        alternatives,
      });
    }
  }
  async resolveAccuracyValidity(payload) {
    try {
      const options = {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(payload),
        },
        { error, message, results } = await (
          await window.fetch("/conditions/validate", options)
        ).json();
      if (error) throw new Error(message);

      return results;
    } catch (error) {
      console.log("Failed processing condition validation: ", error);
      return null;
    }
  }
  async save(payload) {
    try {
      const options = {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(payload),
        },
        { error, message, id } = await (
          await window.fetch("/surveys", options)
        ).json();
      if (error) throw new Error(message);

      this.activeSurveyId = id;
    } catch (error) {
      console.log("Failed saving survey: ", error);
    }
  }

  onAccuracyReview(as, attribute, value) {
    this.emit("accuracy", this.activeSurveyId, as, attribute, value);
  }

  onSaveSuggestions(list) {
    this.emit(
      "suggest",
      this.activeSurveyId,
      list,
      function () {
        this.onSuggestCondition();
      }.bind(this),
    );
  }
  onSuggestCondition(status) {
    this.state.suggesting = status;
  }
  onDismiss(status) {
    this.state.readMore = false;
  }

  onReadMore(args) {
    const { tenantId, test_results } = this.state;

    if (
      tenantId &&
      tenantId.includes("account.upswinghealth") &&
      !test_results
    ) {
      window.parent.postMessage("on-read-more", "*");
    } else {
      this.setState({ selectedDiagnosis: args.content, readMore: true });
    }
  }
}

macro|{ reference, type, question, answers }| name="Question"
  $ const isMultipleAnswer = type === "multiple_answer";

  Row.d-flex.align-items-center
    @col
      div.px-1
        p.black-text.d-flex.align-items-center
          span -- ${state.activePart.toUpperCase()}
          if(state.loading)
            Preloader.font-large-1.px-4 active

        if(!state.loading)
          br
          h4 -- ${question}

      div.px-1.py-3
        <!-- Go back button -->
        if(state.goPrev)
          button.btn.btn-outline-primary on-click("onPrev") -- Go back
        <!-- Multiple answer continue button -->
        if(isMultipleAnswer)
          button.btn.btn-primary.float-right.mx-4 on-click(
            "onNext",
            Object.values(answers)[0].references,
          )
            -- Continue
    @col.col-lg-6.col-12
      if(!state.loading)
        br
        div.row.d-flex.align-items-center.justify-content-center
          for|key, { value, references, image }| in=answers
            if(value)
              $ let imageSrc;
              $ try {
                imageSrc = require(
                  `./../../images/part/${state.activePart} - ${value.replace("/", " or ").toLowerCase().trim()}.png`,
                );
              } catch (error) {
                console.log(
                  "Image not found",
                  `./../../images/part/${state.activePart} - ${value.toLowerCase().trim()}.png`,
                );
              }

              div.col-5.my-50 key=key
                if(isMultipleAnswer)
                  div.answer-card.border-2.rounded-lg.px-1.py-2.cursor-pointer
                    div.checkbox.checkbox-primary
                      input [
                        type="checkbox"
                        no-update
                        id=`multiple-answer-${key}`
                        on-change(
                          "onAnswer",
                          { reference, question, answer: { value, key } },
                          references,
                          true,
                        )
                      ]
                      label.font-weight-400 for=`multiple-answer-${key}`
                        if(imageSrc)
                          img.w-75.rounded-lg [
                            src=imageSrc
                            style="border-radius: 4px 4px 0 0"
                          ]
                          br
                        div.py-25.font-medium-3.black-text
                          -- ${toCapitalCase(value)}
                else
                  div.answer-card.border-2.rounded-lg.px-1.py-2.cursor-pointer.text-center on-click(
                    "onAnswer",
                    { reference, question, answer: { value, key } },
                    references,
                    false,
                  )
                    if(imageSrc)
                      img.w-75.rounded-lg [
                        src=imageSrc
                        style="border-radius: 4px 4px 0 0"
                      ]
                      br
                    div.py-25.font-medium-3.black-text
                      -- ${toCapitalCase(value)}

div.osc.position-fixed.w-100.vh-100
  Row.h-100.d-flex.align-items-center.m-0
    if(!state.suggesting)
      @col class=`${state.activePart ? "d-lg-flex d-none" : "col-12 d-flex"} col-lg-4 vh-100 align-items-center bg-secondary`
        div.w-100.text-center
          div.osc-model
            <!-- <img src=model/> -->

            button [
              class=["body-back", state.activePart == "back" ? "active" : false]
              on-click("onSelectPart", "back")
            ]
              span.bg-black.white-text.text-nowrap.px-1.py-25.round-sm.shadow-lg
                -- Back
            button [
              class=[
                "body-elbow",
                state.activePart == "elbow" ? "active" : false,
              ]
              on-click("onSelectPart", "elbow")
            ]
              span.bg-black.white-text.text-nowrap.px-1.py-25.round-sm.shadow-lg
                -- Elbow
            button [
              class=[
                "body-foot-ankle",
                state.activePart == "foot-ankle" ? "active" : false,
              ]
              on-click("onSelectPart", "foot-ankle")
            ]
              span.bg-black.white-text.text-nowrap.px-1.py-25.round-sm.shadow-lg
                -- Foot & Ankle
            button [
              class=[
                "body-hand-wrist",
                state.activePart == "hand-wrist" ? "active" : false,
              ]
              on-click("onSelectPart", "hand-wrist")
            ]
              span.bg-black.white-text.text-nowrap.px-1.py-25.round-sm.shadow-lg
                -- Hand & Wrist
            button [
              class=[
                "body-knee-lower-leg",
                state.activePart == "knee-lower-leg" ? "active" : false,
              ]
              on-click("onSelectPart", "knee-lower-leg")
            ]
              span.bg-black.white-text.text-nowrap.px-1.py-25.round-sm.shadow-lg
                -- Knee & Lower Leg
            button [
              class=["body-neck", state.activePart == "neck" ? "active" : false]
              on-click("onSelectPart", "neck")
            ]
              span.bg-black.white-text.text-nowrap.px-1.py-25.round-sm.shadow-lg
                -- Neck
            button [
              class=[
                "body-pelvis-hip-thigh",
                state.activePart == "pelvis-hip-thigh" ? "active" : false,
              ]
              on-click("onSelectPart", "pelvis-hip-thigh")
            ]
              span.bg-black.white-text.text-nowrap.px-1.py-25.round-sm.shadow-lg
                -- Pelvis, Hip & Thigh
            button [
              class=[
                "body-shoulder",
                state.activePart == "shoulder" ? "active" : false,
              ]
              on-click("onSelectPart", "shoulder")
            ]
              span.bg-black.white-text.text-nowrap.px-1.py-25.round-sm.shadow-lg
                -- Shoulder

          div.text-center
            h5.white-text -- Select the part that hurts

    @col.col.vh-100.py-3 style=`background:url('${require("./../../images/background.jpg")}') center;background-size: cover`
      if(!state.activeQuestion)
        div.h-100.overflow-auto.d-flex.align-items-center
          div.w-100.text-center
            h3 -- Check your symptom
            <!-- <p>Select the part that hurt from the image</p> -->

      else-if(state.emergency)
        div.px-4.h-100.overflow-auto
          p.font-medium-5.strong.black-text -- See Your Doctor Immediately.
          p.font-medium-3.black-text.py-2
            --
            This can be a sign of a more serious complication, so have it evaluated by your PCP immediately or go to the emergency room.${" "}
            --

          button.btn.btn-primary.px-4.m-25 on-click("onSeeCoach")
            -- Connect with a coach
          button.btn.btn-primary.px-4.m-25 on-click("onRestart") -- Restart

      else-if(state.diagnosis)
        $ const { bests, alternatives, path } = state.diagnosis;

        div.px-4.h-100.overflow-auto
          if(window.CLIENT_ID && input.patient)
            div.py-3.h-100.d-flex.align-items-center.justify-content-center
              div.w-100.text-center
                h2 -- Thank you for completing the Symptom Checker
                br
                button.btn.btn-primary.px-4.py-1.font-medium-2.m-25 on-click(
                  "onRestart",
                )
                  -- Restart
          else
            div.text-left
              <!-- Display best conditions -->
              $ const hasBestConditions = Array.isArray(bests) && bests.length;

              if(hasBestConditions)
                p.font-medium-3.black-text
                  --
                  Hi, Based on your answers, here is the most likely conditions:
                  --

                div.row
                  for|condition, index| of=bests
                    Condition [
                      inrow
                      key=index
                      as="best"
                      review=!!input.physician
                      ...condition
                      on-read-more("onReadMore", condition)
                      on-review("onAccuracyReview")
                    ]

                  if(state.readMore)
                    div.p-25
                      Modal on-dismiss("onDismiss") ...state.selectedDiagnosis
              <!-- Suggest to see doctor: No valid diagnosis -->
              else-if(!alternatives.length)
                h4.mt-1 -- Hmm... we are sorry
                br
                p.font-medium-2.black-text
                  --
                  Based on your answers we could not match them with any condition. Would you like to speak with a coach?
                  --
              <!-- Display alternative diagnosis -->
              if(Array.isArray(alternatives) && alternatives.length)
                if(hasBestConditions)
                  p.black-text -- Other possible conditions includes:
                else
                  p.font-medium-2.black-text
                    --
                    Hi, Based on your answers here are the possible conditions:
                    --

                div.row
                  for|condition, index| of=alternatives
                    Condition [
                      inrow
                      key=index
                      as="alternative"
                      review=!!input.physician
                      on-read-more("onReadMore", condition)
                      ...condition
                      on-review("onAccuracyReview")
                    ]

                  if(state.readMore)
                    div.p-25
                      Modal on-dismiss("onDismiss") ...state.selectedDiagnosis

              br
              if(!input.physician)
                button.btn.btn-primary.px-4.py-1.font-medium-2.m-25 on-click(
                  "onSeeCoach",
                )
                  -- Connect with a coach

              if(input.physician)
                button.btn.btn-danger.px-4.py-1.font-medium-2.m-25 on-click(
                  "onSuggestCondition",
                  true,
                )
                  -- Suggest other conditions
              <!-- Download only most likely diagnosis -->
              <!-- <button.btn.btn-outline-primary.px-4.py-1.font-medium-2.m-25 on-click('onDownload')>Download</button>
              <button.btn.btn-primary.px-4.py-1.font-medium-2.m-25 on-click('onRestart')>Restart</button> -->

            div.py-3
              if(Array.isArray(path) && path.length)
                div
                  h6.black-text
                    --
                    We suggested this condition because you mentioned these symptoms:
                    --
                  br

                  ul.list-unstyled.h-100.overflow-auto
                    for|{ question, answer }, index| of=path
                      li.py-50 key=`path-${index}`
                        p.mb-0 -- ${question}
                        span.text-primary -- ${answer.value}

      else
        div.h-100.overflow-auto
          div.w-100
            Question ...state.activeQuestion

    if(input.physician && state.suggesting)
      @col.col-lg-3.col-md-4.col-12.vh-100.bg-white.border-left.overflow-auto
        Suggest [
          on-save("onSaveSuggestions")
          on-cancel("onSuggestCondition", false)
        ]
