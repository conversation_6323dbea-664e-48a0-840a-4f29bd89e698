.osc .answer-card {
  box-shadow: 0 4px 11px 0 rgb(37 44 97 / 15%), 0 1px 3px 0 rgb(93 100 148 / 20%);
  background-image: linear-gradient(180deg, #fff, #f5f5fa);
}

.osc .answer-card:hover {
  transform: scale(1.02);
  transition: 600ms;
}

.osc-model {
  background-image: url('model.png');
  background-position: 46px 10px;
  background-repeat: no-repeat;
  background-size: 310px auto;
  height: 460px;
  margin: 0 auto 20px;
  position: relative;
  width: 435px;
  z-index: 1;
}

.osc-model button {
  background-color: rgba(242, 242, 242, 0);
  border: none;
  margin: 10px;
  height: 44px;
  /* line-height: 100px; */
  outline: none;
  overflow: hidden;
  cursor: pointer;
  position: absolute;
  z-index: 9;
  width: 44px;
}
.osc-model button span {
  position: absolute;
  z-index: 11;
  margin: -16px 0 0 25px;
}

.osc-model button:before {
  background-color: rgba(255, 255, 255, 0.43);
  border-radius: 50%;
  content: '';
  display: block;
  height: 20px;
  left: 2px;
  position: absolute;
  top: 2px;
  width: 20px;
  animation: pulse-ring 1.25s cubic-bezier(0.215, 0.61, 0.355, 1) infinite
}

@media only screen and (min-width: 768px) {
  .osc-model button:before {
    height: 24px;
    left: 3px;
    top: 3px;
    width: 24px
  }
}

@media only screen and (min-width: 1200px) {
  .osc-model button:before {
    height: 32px;
    left: 4px;
    top: 4px;
    width: 32px
  }
}

.osc-model button:after {
  background-color: rgba(71, 86, 94, 0.8);
  border-radius: 50%;
  content: '';
  display: block;
  height: 4px;
  left: 10px;
  position: absolute;
  top: 10px;
  width: 4px;
  animation: pulse-dot 1.25s cubic-bezier(0.455, 0.03, 0.515, 0.955) -0.4s infinite
}

@media only screen and (min-width: 768px) {
  .osc-model button:after {
    height: 6px;
    left: 12px;
    top: 12px;
    width: 6px
  }
}

@media only screen and (min-width: 1200px) {
  .osc-model button:after {
    height: 8px;
    left: 16px;
    top: 16px;
    width: 8px
  }
}

.osc-model button:hover,
.osc-model button.active {
  overflow: visible;
  transition: transform 0.1s ease-in;
  transform: scale(1.2);
}

.osc-model button.active:before {
  animation: none
}

.osc-model button:hover:before {
  animation: none
}

.osc-model button:hover:after {
  animation: none
}

.osc-model button:hover span {
  opacity: 1;
  transition: opacity 0.1s ease-in
}

@keyframes pulse-ring {
  0% {
    transform: scale(0.5)
  }

  90%,
  100% {
    opacity: 0
  }
}

@keyframes pulse-dot {
  0% {
    transform: scale(0.8)
  }

  50% {
    transform: scale(1)
  }

  100% {
    transform: scale(0.8)
  }
}

.osc-model .body-back { left: 39.5%;top: 35%; }
.osc-model .body-elbow { left: 51.3%;top: 31%; }
.osc-model .body-foot-ankle { left: 45%;top: 85%; }
.osc-model .body-hand-wrist { left: 55%;top: 43%;z-index: 10; }
.osc-model .body-knee-lower-leg { left: 45.95%;top: 66%; }
.osc-model .body-neck { left: 39.45%;top: 13%; }
.osc-model .body-pelvis-hip-thigh { left: 44%;top: 46%; }
.osc-model .body-shoulder { left: 49%;top: 18.5%; }

@media (max-width: 900px) {
  .osc-model .body-back { left: 40.5%;top: 35%; }
  .osc-model .body-elbow { left: 53.3%;top: 33%; }
  .osc-model .body-foot-ankle { left: 47%;top: 87%; }
  .osc-model .body-hand-wrist { left: 56%;top: 44%;z-index: 10; }
  .osc-model .body-knee-lower-leg { left: 47%;top: 67%; }
  .osc-model .body-neck { left: 40.45%;top: 15%; }
  .osc-model .body-pelvis-hip-thigh { left: 46%;top: 46%; }
  .osc-model .body-shoulder { left: 51%;top: 19.5%; }
}

.osc .scanner {
  /* 	center items on the page */
  display: grid;
  place-items: center;
  height: 100%;
}

.osc .outer-scanner {
  /* 	design the circle */
  width: 240px;
  height: 240px;
  border-radius: 50%;
  box-shadow: 0 0 8px 0 #aaa;
  /* 	set its position as relative so that the inner green scanner can be positioned absolute */
  position: relative;
}

.osc .inner-scanner {
  /* 	design the green scanner 40px smaller than the outer-circle */
  width: 200px;
  height: 200px;
  border-radius: 50%;
  /* 	center this green-scanner inside the parent */
  position: absolute;
  top: 20px;
  left: 20px;
  /* 	set the background resembling a scanner */
  background: conic-gradient(#00d0ff2b, #00ddff94);
  /* 	define animation properties and set its easing to linear (default 'ease' doesn't look natural) */
  animation: scan 4s infinite linear;
}

/* make waves using the pseudo-elements for semantic purposes */
.osc .outer-scanner:before,
.osc .outer-scanner:after {
  content: "";
  width: 240px;
  height: 240px;
  border-radius: 50%;
  position: absolute;
  border: 1px solid #eee;
  animation: ripple 2s infinite linear;
}

.osc .outer-scanner:after {
  animation-delay: 1s;
}

/* backdrop for conditions modal */
.backdrop-light {
  background: rgba(255, 255, 255, 0.45);
  backdrop-filter: blur(6.3px);
  -webkit-backdrop-filter: blur(6.3px);
}

.backdrop-dark {
  background: rgba(0, 0, 0, 0.45);
  backdrop-filter: blur(6.3px);
  -webkit-backdrop-filter: blur(6.3px);
}

@keyframes scan {
  to {
    transform: rotate(1turn);
  }
}

@keyframes ripple {
  to {
    transform: scale(2.5);
  }
}