<div.position-absolute.zindex-5.top-0.left-0.right-0.bottom-0.d-flex.align-items-center.justify-content-center>
  <div.position-absolute.top-0.w-100.h-100.animated.fadeIn.faster.backdrop-light
      on-click( () => component.emit('dismiss') )></div>

  <div.position-relative.zindex-1.h-100.animated.fadeIn.faster
      class=(input.class || 'col-10')>
    <Bx.zindex-5.position-absolute.top-0.right-0.font-large-2.text-muted.p-1.mx-25.my-1.cursor-pointer
        type="x"
        title="Close"
        on-click( () => component.emit('dismiss') )/>

    <div.h-100.py-4.overflow-auto>
      <if( input.title )>
        <h4.my-2>${input.title}</h4>
      </if>

      <!-- Description -->
      <if( input.description )>
        <p.font-medium-3.font-weight-bold.black-text>${input.description}</p>
      </if>
      
      <if( input.image )>
        <img src=input.image style="height:350px;"/>
      </if>
      
      <!-- Overview -->
      <if( input.overview && input.overview.body)>
        <p.font-medium-2.black-text>$!{input.overview.body}</p>
      </if>
      
      <!-- Causes -->
      <if( input.causes && input.causes.title && input.causes.body )>
        <h5.my-2>${input.causes.title}</h5>
        <p.font-medium-2.black-text>$!{input.causes.body}</p>
      </if>

      <!-- Symptoms -->
      <if( input.symptoms && input.symptoms.title && input.symptoms.body )>
        <h5.my-2>${input.symptoms.title}</h5>
        <p.font-medium-2.black-text>$!{input.symptoms.body}</p>
      </if>

      <!-- Consult Doctor -->
      <if( input.consultDoctor && input.consultDoctor.title && input.consultDoctor.body )>
        <h5.my-2>${input.consultDoctor.title}</h5>
        <p.font-medium-2.black-text>$!{input.consultDoctor.body}</p>
      </if>

      <!-- No Operative Treatement -->
      <if( input.noOperativeTreatement && input.noOperativeTreatement.title && input.noOperativeTreatement.body )>
        <h5.my-2>${input.noOperativeTreatement.title}</h5>
        <p.font-medium-2.black-text>$!{input.noOperativeTreatement.body}</p>
      </if>

      <!-- Surgical Treatement -->
      <if( input.surgicalTreatement && input.surgicalTreatement.title && input.surgicalTreatement.body )>
        <h5.my-2>${input.surgicalTreatement.title}</h5>
        <p.font-medium-2.black-text>$!{input.surgicalTreatement.body}</p>
      </if>
      
      <!-- Recovery -->
      <if( input.recovery && input.recovery.title && input.recovery.body )>
        <h5.my-2>${input.recovery.title}</h5>
        <p.font-medium-2.black-text>$!{input.recovery.body}</p>
      </if>
      
      <!-- Sources -->
      <if( input.sources )>
        <h5.my-2>Sources</h5>
        <p.font-medium-2.black-text>$!{input.sources}</p>
      </if>
    </div>
  </div>
</div>