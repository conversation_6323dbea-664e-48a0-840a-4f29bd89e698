
import FormHandler from 'markojs-form'
import BodyParts from '../../../data/bodyParts.json'

static function toCapitalCase( str ){
  // Fonction de capitalisation du premier caractère d'un mot
  str.toLowerCase()

  const
  First = str.charAt(0),
  regex = new RegExp('^'+ First )

  return First.toUpperCase() + str.split( regex )[1]
}

class {
  onCreate(){
    this.state = { saving: false }
    this.fhandler = new FormHandler({ key: 'question-form', crosscheck: true })
    this.fhandler.bind(this)
  }
  onInput({ action, activePart, dataset }){
    if( action === 'edit' ) this.fhandler.fill( dataset )
    else {
      this.fhandler.reset()
      this.fhandler.set('parts', [ ...(this.state.form.parts || []), activePart ])
    }
  }
  onSelectBodyPart( value ){
    this.fhandler.error('parts', false )
    this.state.form.parts = this.state.form.parts || []

    !this.state.form.parts.includes( value )
    && this.fhandler.set('parts', [ ...(this.state.form.parts), value ]) 
  }
  onRemoveBodyPart( value ){ 
    this.fhandler.set('parts', this.state.form.parts.filter( each => { return each !== value } )) 
  }
  onAddAnswer(){
    this.fhandler.error('answers', false )
    if( !this.state.form.answers )
      this.state.form.answers = {}

    const nidx = Object.keys( this.state.form.answers ).length + 1

    this.state.form.answers[`${this.input.dataset.reference}:${nidx}`] = { value: '' }
    this.fhandler.set('answers', this.state.form.answers )
  }
  onRemoveAnswer( idx ){
    delete this.state.form.answers[ idx ]
    this.fhandler.set('answers', this.state.form.answers )
  }
  onAnswerInput( idx, e ){
    this.state.form.answers[ idx ] = { value: e.target.value, references: [] }
    this.fhandler.set('answers', this.state.form.answers )
  }
  onSave(){
    const Form = this.state.form
    if( !Array.isArray( Form.parts ) || !Form.parts.length )
      return this.fhandler.error('parts', true )

    if( !Form.question )
      return this.fhandler.error('question', true )
    
    if( !Form.answers )
      return this.fhandler.error('answers', true )

    Object
    .entries( Form.answers )
    .map( ([ idx, dataset ]) => !dataset.value && delete Form.answers[ idx ] )

    if( !Object.keys( Form.answers ).length )
      return this.fhandler.error('answers', true )

    this.state.saving = true
    this.emit( this.input.action, Form, (function(){ this.state.saving = false }).bind(this) )
  }
}

<div.bg-white.w-75.vh-100.position-absolute.zindex-3.top-0.shadow-lg
    style=`transform: translateX(${input.action ? '129' : '-10'}%);transition: 400ms`>
  <if( input.action )>
    <Row.d-flex.align-items-center.px-1.py-50>
      <@col><h6>${toCapitalCase( input.action )} questions</h6></@col>
      <@col.col-4.text-right>
        <button.btn.btn-white.btn-sm.rounded-lg
                on-click( () => component.emit('dismiss') )>Close</button>
      </@col>
    </Row>

    <Switch by=input.action>
      <@case is=['add', 'edit']>
        <div.px-2.py-1 key="question-form">
          <div.form-group>
            <label>Body parts</label>
            <ul.list-inline>
              <for|part, index| of=(state.form.parts || [])>
                <li.d-inline-flex.align-items-center.py-25.m-25.bg-light.round key=index>
                  <span.mx-75.white-text>${part}</span>
                  <Bx.font-medium-4.mx-25.rounded-circle.bg-white.p-25.cursor-pointer 
                      type="x" 
                      title="Remove"
                      on-click('onRemoveBodyPart', part )/>
                </li>
              </for>
            </ul>

            <Select.bg-white.rounded.px-25.border.shadow-none
                    size="lg"
                    placeholder="Add body part"
                    options=BodyParts
                    value=""
                    on-select('onSelectBodyPart')/>
            <if( state.formError.parts )>
              <p.font-small-3.text-warning.mt-0>No body part selected</p>
            </if>
          </div>
          
          <div.form-group>
            <label>Question</label>
            <textarea.form-control.form-control-lg.border
                      type="text"
                      name="question"
                      value=state.form.question
                      placeholder="Type question here"
                      style="resize:none;min-height:8rem;"
                      on-change('__onChange')></textarea>
            <if( state.formError.question )>
              <p.font-small-3.text-warning.mt-0>Question field is required</p>
            </if>
          </div>

          <div.checkbox.checkbox-primary.my-50>
            <input type="checkbox"
                    id="entrypoint"
                    name="entrypoint"
                    checked=state.form.entrypoint
                    on-change('__onChecked')/>
            <label for="entrypoint">Use as start question</label>
          </div>
          
          <div.form-group.py-1>
            <label>Answers</label>
            <if( state.formError.answers )>
              <p.font-small-3.text-warning.mt-0>Add some possible answers to the question</p>
            </if>

            <ul.list-unstyled>
              <for|idx, { value }| in=(state.form.answers || {})>
                $ console.log( idx )
                <li.input-group.border-bottom key=idx>
                  <input.form-control.border-none.shadow-none
                        type="text"
                        placeholder="Type answer here"
                        value=value
                        on-change('onAnswerInput', idx )/>
                  <div.input-group-append>
                    <button.btn.btn-white.px-50.text-center on-click('onRemoveAnswer', idx )>
                      <Bx.font-medium-4 type="x"/>
                    </button>
                  </div>
                </li>
              </for>
            </ul>
            
            <button.btn.btn-light.btn-sm.float-right on-click('onAddAnswer')>Add answer</button>
          </div>

          <br>
          <button.btn.btn-primary.btn-lg.btn-block 
                  disabled=!state.form.question
                  on-click('onSave')>
            Save <Preloader.font-medium-5 active=state.saving/>
          </button>
        </div>
      </@case>
      <@case is="link">
        <form.px-2.py-1 key="link-form">
          <div.form-group>
            <SearchBar placeholder="Find question"
                        on-query('onFindQuestion')
                        on-no-query('onFindQuestion', false )/>
            <if( state.formError.question )>
              <p.font-small-3.text-warning.mt-0>Question field is required</p>
            </if>
          </div>
        </form>
      </@case>
    </Switch>
  </if>
</div>