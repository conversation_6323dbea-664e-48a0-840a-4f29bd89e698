
import Drawflow from '@fabrice8/drawflow'
import '@fabrice8/drawflow/dist/drawflow.min.css'
import './style.css'

class {
  onCreate(){
    this.flow = null
    this.nodes = {}
    this.entrypoint = null
  }
  onInput({ nodes }){
    if( !Array.isArray( nodes ) && !nodes.length ) return

    nodes.map( node => {
      this.nodes[ node.reference ] = node
      if( node.entrypoint ) this.entrypoint = node // Record entrypoint/start node
    } )

    // Rerender chart on new input
    this.flow && this.renderChart()
  }
  onMount(){ this.initChart() }
  onUpdate(){ this.initChart() }
  initChart(){
    this.flow = new Drawflow( this.getEl() )
    
    this.flow.editor_mode = this.input.edit ? 'edit' : 'fixed'
    this.flow.start()
    
    Array.isArray( this.input.nodes ) 
    && this.input.nodes.length
    && this.renderChart()
  }
  createNode( node, { x, y } ){
    const
    { reference, question, answers, parents } = node,
    inputCount = parents ? parents.length : 0,
    outputCount = answers ? Object.keys( answers ).length : 0,
    html = `<div df-name>${question}</div>`,
    data = { reference: node.reference }
    
    this.flow.addNode( 'main', inputCount, outputCount, x, y, 'sc-node', data, html, Number( reference ) )
  }
  renderChart( node, position = { x: 0, y: 0 } ){
    if( !node && !this.entrypoint ) return
    
    const { reference, answers } = node || this.entrypoint
    if( !answers ) return

    // Create entrypoint node
    !node && this.createNode( this.entrypoint, position )
    
    const 
    margin = 60,
    Answers = Object.entries( answers ),
    alen = Answers.length

    let edge = ( alen % 2 ) == 1 ?
                    ( ( alen > 1 ? ( ( alen - 1 ) / 2 ) : 1 ) * margin ) - ( margin / 2 ) // Even
                    : ( ( alen / 2 ) * margin ) + ( margin / 2 )

    position.x += 300
    edge += position.y // Use this node's `y` position as reference to child nodes

    function getInputIdx({ parents }, reference ){
      for( const x in parents )
        if( parents[x] == reference ) return x
      
      return 1
    }

    Answers.map( ([ idx, dataset ], index ) => {
      
      edge -= margin // same childen y position float
      dataset.references.map( inputReference => {
        // Legacy support for formated data references
        idx = idx.includes(':') ? index + 1 : idx

        const
        childNode = this.nodes[ inputReference ],
        childPosition = { x: position.x, y: edge }
        
        try { this.flow.getNodeFromId( inputReference ) }
        catch( error ){ 
          this.createNode( childNode, childPosition ) }

        this.flow.addConnection( reference, inputReference, `output_${idx}`, `input_${getInputIdx( childNode, reference )}` )
        this.renderChart( childNode, childPosition )
      } )
    } )
  }
}

<div.w-100.h-100></div>