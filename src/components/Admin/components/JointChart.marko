import dagre, { graphlib } from "dagre";
import {
  dia,
  layout,
  shapes,
  connectors,
  highlighters,
  connectionStrategies,
} from "jointjs";
import { includeObject } from "../../../utils";
class {
  onCreate() {
    this.state = {
      zoom: 1,
      correction: null,
      tempTrace: null,
    };

    this.activePart = null;
  }
  onMount() {
    this.init();
  }
  onInput({ editable, questions, path }) {
    // Render map
    this.renderGraph(questions, path);

    if (this.paper) this.paper.options.interactive = editable;
  }

  init() {
    const self = this,
      container = this.getEl();

    (this.graph = new dia.Graph({}, { cellNamespace: shapes })),
      (this.paper = new dia.Paper({
        model: this.graph,
        cellViewNamespace: shapes,
        width: "100%",
        height: "100%",
        gridSize: 20,
        drawGrid: { name: "mesh" },
        async: true,
        sorting: dia.Paper.sorting.APPROX,
        // background: { color: '#F3F7F6' },
        validateConnection: (sv, _sm, tv, _tm) => sv !== tv,
        restrictTranslate: true,
        linkPinning: false,
        multiLinks: false,
        interactive: true,
        // frozen: true,
        clickThreshold: 10,
        // connectionStrategy: connectionStrategies.pinAbsolute,
        defaultLink: () => {
          return new shapes.standard.Link();
        },
        defaultLinkAnchor: { name: "center" },
        defaultConnectionPoint: { name: "bbox" },
        defaultAnchor: { name: "modelCenter" },
        defaultConnector: {
          name: "smooth",
          args: {
            direction: connectors.curve.Directions.VERTICAL,
            targetDirection: connectors.curve.TangentDirections.OUTWARDS,
            cornerRadius: 20,
          },
        },
        /*
      defaultRouter: {
        name: 'manhattan',
        args: {
          step: 10,
          endDirections: ['bottom'],
          startDirections: ['top'],
          padding: { bottom: 20 }
        }
      }
      */
      }));

    container.appendChild(this.paper.el);

    this.PORT_WIDTH = 100;
    this.PORT_HEIGHT = 20;
    this.PORT_GAP = 25;
    this.LETTER_SIZE = 8;
    this.CLEARING = false;
    this.HIGHLIGHT_COLOR = "#26a465";
    this.HIGHLIGHT_WARN_COLOR = "#ef8903";
    this.HIGHLIGHT_DELETE_COLOR = "#FF4365";
    this.HIGHLIGHT_TEMP_COLOR = "#5A8DEE";
    this.HIGHLIGHT_TEXT_COLOR = "#ffffff";

    function autosize(element, isPort) {
      var view = self.paper.findViewByModel(element);
      var text = view.findBySelector("label")[0];

      // console.log( view.findBySelector('label').getStyle() )

      if (text) {
        var padding = 50;
        // Use bounding box without transformations so that our auto-sizing works
        // even on e.g. rotated element.
        var bbox = text.getBBox();
        // Give the element some padding on the right/bottom.
        element.resize(bbox.width + padding, bbox.height + padding);
      }
    }

    this.graph.on("add", (cell) => {
      if (cell.isLink()) return;
      setTimeout(() => {
        autosize(cell);
        /*
        cell.getPorts().map( port => {
          var
          view = paper.findViewByModel( cell ),
          labels = view.findBySelector('portLabel')

          // console.log( labels )
        } )
        */
      }, 1);
    });
    this.graph.on("change", (cell, opt) => !cell.isLink() && autosize(cell));
    this.graph.on("remove", (cell, collection, opt) => {
      if (this.CLEARING) return;
      this.deleteElement(cell);
    });

    this.selectedElement = null;

    // Select node
    this.paper.on("element:pointerclick", (view) => {
      // Remove existing highlight
      this.selectedElement &&
        dia.HighlighterView.remove(this.selectedElement.view);

      highlighters.mask.add(view, { selector: "root" }, "node-highlight", {
        deep: true,
        attrs: {
          stroke: this.HIGHLIGHT_TEMP_COLOR,
          "stroke-width": 3,
        },
      });

      this.selectedElement = { isLink: false, view };
    });
    // Select link
    this.paper.on("link:pointerclick", (view) => {
      // Remove existing highlight
      this.selectedElement &&
        dia.HighlighterView.remove(this.selectedElement.view);

      highlighters.mask.add(view, { selector: "line" }, "link-highlight", {
        // Draw the highlighter under the LinkView
        layer: "back",
        attrs: {
          stroke: this.HIGHLIGHT_TEMP_COLOR,
          "stroke-width": 3,
          "stroke-linecap": "square",
        },
      });

      this.selectedElement = { isLink: true, view };

      if (!this.input.editable && Array.isArray(this.input.path)) {
        const source = view.model.get("source"),
          target = view.model.get("target"),
          data = view.model.toJSON();
        let active = false;

        if (
          data &&
          data.attrs &&
          data.attrs.line &&
          data.attrs.line.stroke === this.HIGHLIGHT_COLOR
        )
          active = true;

        this.state.tempTrace = {
          from: source.id,
          fromPort: source.port,
          to: target.id,
          traces: [],
          autocorrect: true,
          active,
        };
      }
    });
    this.paper.on("element:magnet:pointerclick", (view, e) => {
      if (this.input.editable) return;

      const portId = view.findAttribute("port", e.target);
      if (!portId || !this.state.tempTrace) return;

      let active = false;
      const port = view.model.getPort(portId);
      if (
        port &&
        port.attrs &&
        port.attrs.portBody &&
        port.attrs.portBody.fill === this.HIGHLIGHT_COLOR
      )
        active = true;

      const [_, qref, aref] = portId.match(/([0-9]+):([0-9]+)/);

      // Invalid trace selection
      if (this.state.tempTrace.to === qref) {
        this.state.tempTrace.traces.push({
          from: qref,
          fromPort: portId,
          traces: [],
          autocorrect: true,
          active,
        });

        this.renderPath({ brokens: [this.state.tempTrace] }, true);
      }

      // Clear
      // this.state.tempTrace = null
      dia.HighlighterView.remove(this.selectedElement.view);
    });
    // Unselect elements
    this.paper.on("blank:pointerclick", (view) => {
      if (!this.selectedElement) return;

      dia.HighlighterView.remove(this.selectedElement.view);
      this.selectedElement = null;
    });
    this.paper.on(
      "link:connect",
      (linkView, e, cellView, magnet, arrowhead) => {
        if (!this.input.editable) return;

        const link = linkView.model,
          source = link.get("source"),
          target = link.get("target");

        if (!target || !source || !target.id || !source.id || !source.port)
          return;

        // Send linking details
        this.emit("link", {
          from: source.id,
          fromPort: source.port,
          to: target.id,
        });
      },
    );

    // Delete element using keyboard
    document.addEventListener(
      "keyup",
      (e) => {
        if (
          !this.input.editable ||
          !this.selectedElement ||
          (e.code !== "Backspace" && e.code !== "Delete")
        )
          return;

        const { isLink, view } = this.selectedElement;

        view.model.remove();
        this.selectedElement = null;
      },
      false,
    );
  }

  createNode(reference, text, ports) {
    const width = 2 * (this.LETTER_SIZE * (0.6 * text.split("").length + 1)),
      node = new shapes.standard.Rectangle({
        id: reference,
        size: {
          width,
          height: 70,
        },
        markup: [
          {
            tagName: "rect",
            selector: "body",
          },
          {
            tagName: "text",
            selector: "label",
          },
        ],
        useFirstSubpath: true,
        attrs: {
          root: {
            magnet: true,
          },
          body: {
            width: "calc(w)",
            height: "calc(h)",
            rx: 5,
            ry: 5,
            fill: "#ffffff",
            stroke: "#555",
            strokeWidth: 4,
            // round the mask at the corners of paths
            "stroke-linejoin": "round",
            // round the mask at the end of open subpaths
            "stroke-linecap": "round",
          },
          label: {
            fontWeight: "bold",
            fontSize: 20,
            fontFamily: "sans-serif",
            fill: "#333333",
            stroke: "#555",
            strokeWidth: 1,
            paintOrder: "stroke",
            text,
          },
        },
        ports: {
          groups: {
            answers: {
              markup: [
                {
                  tagName: "rect",
                  selector: "portBody",
                },
                {
                  tagName: "text",
                  selector: "portLabel",
                },
              ],
              attrs: {
                portBody: {
                  x: 0,
                  y: -this.PORT_HEIGHT / 2,
                  rx: 5,
                  ry: 5,
                  width: "calc(w)",
                  height: "calc(h)",
                  fill: "#efffff",
                  stroke: "#333333",
                  strokeWidth: 3,
                  "stroke-linejoin": "round",
                  "stroke-linecap": "round",
                  magnet: "active",
                  cursor: "grab",
                },
                portLabel: {
                  x: "calc(0.5*w)",
                  textAnchor: "middle",
                  textVerticalAnchor: "middle",
                  pointerEvents: "none",
                  fontWeight: "bold",
                  fontSize: 18,
                  fontFamily: "sans-serif",
                },
              },
              size: { width: this.PORT_WIDTH, height: this.PORT_HEIGHT },
            },
          },
          items: [],
        },
      });

    this.setPorts(node, width, ports);

    return node;
  }
  setPorts(el, mainWidth, ports) {
    let totalWidth = 0,
      answerPorts = ports.map((each, index) => {
        each.width =
          2 * (this.LETTER_SIZE * (0.6 * each.value.split("").length + 1));
        totalWidth += each.width + (index > 0 ? this.PORT_GAP : 0);

        return each;
      });

    const diff = (totalWidth - mainWidth) / 2;

    let previousX = 0,
      previousWidth = 0;

    answerPorts = answerPorts.map(({ reference, value, width }, index) => {
      let x;

      index > 0
        ? (x = previousX + previousWidth + this.PORT_GAP) // Next port's X position
        : (x = -diff); // First port X position

      previousX = x;
      previousWidth = width;

      return {
        id: reference,
        group: "answers",
        attrs: {
          portLabel: { text: value },
        },
        size: { width },
        args: { x, y: "100%" },
      };
    });

    el.addPorts(answerPorts);
    // el.prop( ['ports', 'items'], answerPorts, { rewrite: true })
  }
  highlightCell(cell, portId, action) {
    if (!cell || !cell.findView) return;

    let color;
    switch (action) {
      case "delete":
        color = this.HIGHLIGHT_DELETE_COLOR;
        break;
      // case 'warn': color = this.HIGHLIGHT_WARN_COLOR; break
      case "warn":
        return;
      case "temp":
        color = this.HIGHLIGHT_TEMP_COLOR;
        break;
      default:
        color = this.HIGHLIGHT_COLOR;
    }

    if (cell.isLink()) cell.prop("attrs/line", { stroke: color });
    else {
      cell.prop("attrs/body", { stroke: color });

      if (portId) {
        cell.portProp(portId, "attrs/portBody", { fill: color, stroke: color });
        cell.portProp(portId, "attrs/portLabel", {
          fill: this.HIGHLIGHT_TEXT_COLOR,
        });
      }
    }
  }
  deleteElement(cell) {
    if (cell.isLink()) {
      const source = cell.get("source"),
        target = cell.get("target");

      if (!target || !source || !target.id || !source.id || !source.port)
        return;

      this.emit("delete", [
        { from: source.id, fromPort: source.port, to: target.id },
      ]);
    } else console.log("Delete node");
  }

  getInputData(questions) {
    const model = [],
      links = [];

    questions.map(({ parts, entrypoint, reference, question, answers }) => {
      if (entrypoint) return;

      const entry = {
        reference,
        question,
        answers: [],
      };

      Object.entries(answers).map(([aref, { type, value, references }]) => {
        entry.answers.push({ type, value, reference: aref, references });

        references.map((each) => {
          const link = {
            from: reference,
            fromPort: aref,
            to: each,
          };

          // Create link only once
          !includeObject(links, link, ["from", "fromPort", "to"]) &&
            links.push(link);
        });
      });

      model.push(entry);
    });

    return { model, links };
  }
  getPathData(questions, path) {
    if (!Array.isArray(questions)) return [];

    const joinctions = [];
    let brokens = [];

    path.map((each) => {
      const [_, _qref, _aref] = each.match(/([0-9]+):([0-9]+)/);

      questions.forEach(({ reference, answers }) => {
        reference === _qref &&
          Object.entries(answers).forEach(
            ([aref, { type, value, references }]) => {
              // Dead end joinction
              if (!references.length) {
                const joinction = { from: reference, fromPort: aref };

                !path.includes(aref)
                  ? !path
                      .map((_) => {
                        return _.split(":")[0];
                      })
                      .includes(reference) && brokens.push(joinction)
                  : !includeObject(joinctions, joinction, [
                      "from",
                      "fromPort",
                      "to",
                    ]) && joinctions.push(joinction);
              }
              // Link
              else
                aref === _ &&
                  references.forEach((each) => {
                    const joinction = {
                      from: reference,
                      fromPort: aref,
                      to: each,
                    };

                    !path
                      .map((_) => {
                        return _.split(":")[0];
                      })
                      .includes(each)
                      ? brokens.push(joinction)
                      : // Create link only once
                        !includeObject(joinctions, joinction, [
                          "from",
                          "fromPort",
                          "to",
                        ]) && joinctions.push(joinction);
                  });
            },
          );
      });
    });

    function tracePath(from) {
      if (!from) return;

      const traces = [];
      let autocorrect = false;

      for (let x = 0; x < questions.length; x++) {
        const { reference, answers } = questions[x];

        if (reference === from) {
          if (
            path
              .map((_) => {
                return _.split(":")[0];
              })
              .includes(from)
          ) {
            autocorrect = true;
            return { autocorrect, traces };
          }

          Object.entries(answers).map(([aref, { type, value, references }]) => {
            references.length &&
              references.map((each) => {
                const results = tracePath(each),
                  link = {
                    from,
                    fromPort: aref,
                    to: each,
                    ...results,
                  };

                // Populate autocorrect signal to the thread
                if (!autocorrect && results.autocorrect)
                  autocorrect = results.autocorrect;

                // Record link only once
                !includeObject(traces, link, ["from", "fromPort", "to"]) &&
                  traces.push(link);
              });
          });
          break;
        }
      }

      return { autocorrect, traces };
    }

    // brokens = brokens.map( each => { return { ...each, ...tracePath( each.to ) } } )

    return { joinctions, brokens };
  }

  renderDiagram({ model, links }) {
    // Clear existing diagram
    this.CLEARING = true;
    this.graph && this.graph.clear();

    const self = this,
      refsRecord = [];

    model.map((each) => {
      const { reference, question, answers } = each,
        node = this.createNode(reference, question, answers);

      refsRecord.push(reference);

      this.graph.addCell(node);
    });
    links.map(({ from, fromPort, to }) => {
      if (!refsRecord.includes(from) || !refsRecord.includes(to)) return;

      const link = new shapes.standard.Link({
        source: { id: from, port: fromPort },
        target: { id: to },
      });

      this.graph.addCell(link);
    });

    layout.DirectedGraph.layout(this.graph, {
      dagre,
      graphlib,
      // setVertices: true,
      setLabels: true,
      ranker: "network-simplex", // network-simplex, tight-tree, longer-path
      rankDir: "TB", // TB, BT, RL, LR
      align: "UL", // UL, UR, DL, DR
      rankSep: 200,
      edgeSep: 80,
      nodeSep: 300,
      setLinkVertices: false,
    });

    // Automatically scale the content to fit the paper.
    const graphBBox = this.graph.getBBox();

    function transformToFitContent() {
      self.paper.transformToFitContent({
        padding: 30,
        contentArea: graphBBox,
        verticalAlign: "middle",
        horizontalAlign: "middle",
      });
    }

    this.CLEARING = false;

    window.addEventListener("resize", () => transformToFitContent());
    transformToFitContent();
  }
  renderPath({ joinctions, brokens }, isExtend) {
    const self = this,
      Elements = this.graph.getElements(),
      Links = this.graph.getLinks();

    this.state.correction = {
      type: isExtend ? "extend" : "fix",
      refs: [],
    };

    function drawBrokenPath(
      { from, fromPort, to, traces, autocorrect, active },
      parent,
    ) {
      if (!autocorrect) return;

      // Deletion fix detection
      // if( active ) self.state.correction.type = 'delete'

      // Record correctable path point
      self.state.correction.refs.push({
        action: active ? "remove" : "add",
        reference: fromPort,
      });

      // Highlighting action type
      const htype = isExtend ? (active ? "delete" : "temp") : "warn";

      // Highlight node
      for (const node of Elements)
        if (from === node.id && node.hasPort(fromPort)) {
          self.highlightCell(node, fromPort, htype);
          break;
        }

      // Highlight link
      for (const link of Links) {
        const source = link.source(),
          target = link.target();

        if (
          parent.to == from &&
          parent.from === source.id &&
          parent.fromPort === source.port &&
          parent.to === target.id
        ) {
          self.highlightCell(link, false, htype);
          continue;
        }

        if (
          from === source.id &&
          fromPort === source.port &&
          to === target.id
        ) {
          self.highlightCell(link, false, htype);
          continue;
        }
      }

      Array.isArray(traces) &&
        traces.length &&
        traces.map((trace) => drawBrokenPath(trace, { from, fromPort, to }));
    }

    Elements.forEach((node) => {
      Array.isArray(joinctions) &&
        joinctions.length &&
        joinctions.map((each) => {
          (each.from === node.id || each.to === node.id) &&
            node.hasPort(each.fromPort) &&
            this.highlightCell(node, each.fromPort);
        });

      Array.isArray(brokens) &&
        brokens.length &&
        brokens.map(({ from, fromPort, to, traces, autocorrect, active }) => {
          if (from !== node.id || !autocorrect) return;

          // Deletion fix detection
          if (active) this.state.correction.type = "delete";

          // Record correctable path point
          this.state.correction.refs.push({
            action: !active ? "add" : null,
            reference: fromPort,
          });

          Array.isArray(traces) &&
            traces.length &&
            traces.map((trace) =>
              drawBrokenPath(trace, { from, fromPort, to }),
            );
        });
    });

    Array.isArray(joinctions) &&
      joinctions.length &&
      Links.forEach((link) => {
        const source = link.source(),
          target = link.target();

        joinctions.map((each) => {
          each.from === source.id &&
            each.fromPort === source.port &&
            each.to === target.id &&
            this.highlightCell(link);
        });
      });
  }
  renderGraph(questions, path) {
    // Render questions nodes
    Array.isArray(questions) &&
      questions.length &&
      this.renderDiagram(this.getInputData(questions));

    this.state.correction = null;
    this.state.tempTrace = null;

    // Render diagnosis/condition path
    Array.isArray(path) &&
      path.length &&
      this.renderPath(this.getPathData(questions, path));
  }

  onZoom(_) {
    const nextZoom = _ == "in" ? this.state.zoom + 1 : this.state.zoom - 1;
    if (nextZoom > 5 || nextZoom < 1) return;

    this.state.zoom = nextZoom;
    $(".joint-paper").css({
      transform: `scale(${nextZoom})`,
      "transform-origin": "top left",
    });
  }
  onCancelCorrection() {
    this.state.correction = null;

    // Re-render graph to previous state
    const { questions, path } = this.input;
    this.renderGraph(questions, path);
  }
}

<div
  no-update
  class="w-100 h-100 position-relative overflow-auto"
  id="joinchart"
/>

<ul class="list-inline position-absolute zindex-2 d-flex align-items-center bottom-0 right-0 m-2">
  <if(
    state.correction &&
    Array.isArray(state.correction.refs) &&
    state.correction.refs.length
  )>
    <li class="mx-50">
      <if(state.correction.type == "fix")>
        <!-- <button.btn.btn-danger.btn-lg.btn-block.round.shadow-lg.text-nowrap.shadow-lg
                on-click( () => component.emit('apply-correction', state.correction ) )>Fix path</button> -->
      </if>
      <else-if(state.correction.type == "delete")>
        <button
          on-click(() => component.emit("apply-correction", state.correction))
          class="btn btn-danger btn-lg btn-block round shadow-lg text-nowrap shadow-lg"
        >
          Delete path
        </button>
      </else-if>
      <else>
        <button
          on-click(() => component.emit("apply-correction", state.correction))
          class="btn btn-primary btn-lg btn-block round shadow-lg text-nowrap shadow-lg"
        >
          Save path
        </button>
      </else>
    </li>

    <if(["extend", "delete"].includes(state.correction.type))>
      <li class="mx-50">
        <button
          on-click("onCancelCorrection")
          class="btn btn-outline-primary btn-lg btn-block round shadow-lg text-nowrap shadow-lg bg-white"
        >
          Cancel
        </button>
      </li>
    </if>
  </if>

  <if(state.zoom > 1)>
    <li class="mx-50">
      <Bx
        type="zoom-out"
        on-click("onZoom", "out")
        class="font-large-1 rounded-circle shadow-lg p-1 bg-dark white-text cursor-pointer"
      />
    </li>
  </if>

  <if(state.zoom < 5)>
    <li class="mx-50">
      <Bx
        type="zoom-in"
        on-click("onZoom", "in")
        class="font-large-1 rounded-circle shadow-lg p-1 bg-dark white-text theme-bg cursor-pointer"
      />
    </li>
  </if>
</ul>
