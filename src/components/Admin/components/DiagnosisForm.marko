
import FormHandler from 'markojs-form'
import BodyParts from '../../../data/bodyParts.json'

static function toCapitalCase( str ){
  // Fonction de capitalisation du premier caractère d'un mot
  str.toLowerCase()

  const
  First = str.charAt(0),
  regex = new RegExp('^'+ First )

  return First.toUpperCase() + str.split( regex )[1]
}

class {
  onCreate(dataset){
    this.state = {
      saving: false,
      catalogue: {} ,
    }
    //{attribute:  dataset.attribute}

    this.fhandler = new FormHandler({ key: 'diagnosis-form', crosscheck: true })
    this.fhandler.bind(this)

    this.hasCatalogueChange = true
  }
  onInput({ action, activePart, dataset }){
    if( action === 'edit' ){
      this.fhandler.fill( dataset )
      this.getCatalogue( dataset.attribute )
    }
    else {
      this.fhandler.reset()
      this.fhandler.set('part', activePart )
    }
  }
  onSelectBodyPart( value ){
    this.fhandler.error('parts', false )
    this.state.form.parts = this.state.form.parts || []

    !this.state.form.parts.includes( value )
    && this.fhandler.set('parts', [ ...(this.state.form.parts), value ]) 
  }
  onRemoveBodyPart( value ){ 
    this.fhandler.set('parts', this.state.form.parts.filter( each => { return each !== value } )) 
  }
    onCatalogueChange( updates ){ 
    this.hasCatalogueChange = true
    this.state.catalogue = updates
  }
  onSave(){
    const
    Form = this.state.form,
    Catalogues =  this.state.catalogue  

    if( !Form.parts )
      return this.fhandler.error('parts', true )

    if( !Form.name )
      return this.fhandler.error('name', true )
    
    Form.attribute = Form.name.replace(/(\s+|\/)/g, '-').replace(/[\(\)]/g, '').toLowerCase()
    Catalogues.attribute = Form.attribute
    
    this.state.saving = true

    // Edit or update diangosis: Defined by `this.input.action`
    this.emit( this.input.action, Form, (function(){ this.state.saving = false }).bind(this) )

    // Upsert catalogue changes seperately
    if( true ){
      this.emit('upsert-catalogue', Catalogues )
      this.hasCatalogueChange = false
    }
  }
  async getCatalogue( attribute ){
    try {
      const { error, message, catalogue } = await ( await window.fetch(`/catalogue/${attribute}`) ).json()
      if( error ) throw new Error( message )

      this.state.catalogue = catalogue
    }
    catch( error ){ console.log('Failed fetching diagnosis catalogue data: ', error ) }
  }
}

<div.bg-white.w-75.vh-100.position-absolute.zindex-3.top-0.shadow-lg.border-left.overflow-auto
    style=`transform: translateX(${input.action ? '129' : '-10'}%);transition: 400ms`>
  <if( input.action )>
    <Row.d-flex.align-items-center.px-1.py-50>
      <@col>
        <h6>${toCapitalCase( input.action )} diagnosis </h6></@col>
      <@col.col-4.text-right>
        <button.btn.btn-white.btn-sm.rounded-lg
                on-click( () => component.emit('dismiss') )>Close</button>
      </@col>
    </Row>

    <Switch by=input.action>
      <@case is=['add', 'edit']>
        <div key="diagnosis-form">
          <div.px-2.py-1>
            <!-- <div.form-group>
              <label>Body parts</label>
              <Select.bg-white.rounded.px-25.shadow-none.border
                      size="lg"
                      placeholder="Select body part"
                      options=BodyParts
                      name="part"
                      value=state.form.part
                      on-select('__onChange')/>
              <if( state.formError.part )>
                <p.font-small-3.text-warning.mt-0>No body part selected</p>
              </if>
            </div> -->
            
            <div.form-group>
              <label>Body parts</label>
              <ul.list-inline>
                <for|part, index| of=(state.form.parts || [])>
                  <li.d-inline-flex.align-items-center.py-25.m-25.bg-light.round key=index>
                    <span.mx-75.white-text>${part}</span>
                    <Bx.font-medium-4.mx-25.rounded-circle.bg-white.p-25.cursor-pointer 
                        type="x" 
                        title="Remove"
                        on-click('onRemoveBodyPart', part )/>
                  </li>
                </for>
              </ul>

              <Select.bg-white.rounded.px-25.border.shadow-none
                      size="lg"
                      placeholder="Add body part"
                      options=BodyParts
                      value=""
                      on-select('onSelectBodyPart')/>
              <if( state.formError.parts )>
                <p.font-small-3.text-warning.mt-0>No body part selected</p>
              </if>
            </div>
            
            <div.form-group>
              <label>Name</label>
              <textarea.form-control.form-control-lg.border
                        type="text"
                        name="name"
                        value=state.form.name
                        placeholder="Name"
                        style="resize:none;min-height:6rem;"
                        on-change('__onChange')></textarea>
              <if( state.formError.name )>
                <p.font-small-3.text-warning.mt-0>name field is required</p>
              </if>
            </div>
          </div>

          <if(true)>
            <CatalogueForm data=state.catalogue on-change('onCatalogueChange')/>
          </if>
          
          <section.px-2.py-1>
            <button.btn.btn-primary.btn-lg.btn-block 
                    disabled=!state.form.name
                    on-click('onSave')>
              Save <Preloader.font-medium-5 active=state.saving/>
          </button>
          </section>
        </div>
      </@case>
    </Switch>
  </if>
</div>