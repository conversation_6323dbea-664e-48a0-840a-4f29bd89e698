
class {
  onCreate(){ this.state = { collapsed: false } }
  onCollapse(){ this.state.collapsed = !this.state.collapsed }
}

<li.nav-item.border-bottom.pb-4.cursor-pointer>
  <div.py-50 on-click('onCollapse')>
    <Bx.font-medium-5.px-75.float-right.cursor-pointer
        type=`chevron-${!state.collapsed ? 'down' : 'up'}`/>
    <span.font-small-3.text-muted>#${input.reference}</span>

    <if( input.entrypoint )>
      <span.badge.badge-primary.mx-1>Start</span>
    </if>
  </div>
  <div.font-medium-1.black-text.pr-5 on-click('onCollapse')>${input.question}</div>

  <if( state.collapsed )>
    <div.py-1.animated.fadeIn.fast>
      <label.font-small-2.text-muted>Possible answers</label>
      <ul.list-inline.pr-4.py-50.mt-1>
        <for|idx, { value, references }| in=input.answers>
          <li.d-inline-flex.align-items-center.py-25.m-25.bg-light.round>
            <span.mx-75.white-text>${value}</span>
            <Bx.font-medium-4.mx-25.rounded-circle.bg-white.p-25.cursor-pointer
                type="plus"
                title="Link to"
                on-click( () => component.emit('action', 'link', { linkTo: `${input.reference}:${idx}` }) )/>
          </li>
        </for>
      </ul>

      <br>
      <Row.m-0>
        <@col.col.pl-0>
          <button.btn.btn-primary.btn-block.rounded-lg 
                  on-click( () => component.emit('action', 'edit') )>Edit</button>
        </@col>
        <@col.col.pl-0>
          <button.btn.btn-outline-danger.btn-block.rounded-lg 
                  on-click( () => component.emit('delete') )>Delete</button>
        </@col>
      </Row>
    </div>
  </if>
</li>