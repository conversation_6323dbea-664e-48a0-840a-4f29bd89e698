
import FormHandler from 'markojs-form'
import BodyParts from '../../../data/bodyParts.json'

static function toCapitalCase( str ){
  // Fonction de capitalisation du premier caractère d'un mot
  str.toLowerCase()

  const
  First = str.charAt(0),
  regex = new RegExp('^'+ First )

  return First.toUpperCase() + str.split( regex )[1]
}
static const SameSet = {
  causes: 'Causes',
  symptoms: 'Symptoms',
  consultDoctor: 'Consult Doctor',
  noOperativeTreatement: 'No Operative Treatement',
  surgicalTreatement: 'Surgical Treatement',
  recovery: 'Recovery'
}

class {
  onCreate({ data }){
    this.state = { activeSection: 'about' }
    
    this.fhandler = new FormHandler({ key: 'diagnosis-form', crosscheck: true })
    this.fhandler.bind(this, {
      link: '',
      content: {
        image: '',
        title: '',
        description: '',
        overview: { body: '' },
        causes: { title: '', body: '' },
        symptoms: { title: '', body: '' },
        consultDoctor: { title: '', body: '' },
        noOperativeTreatement: { title: '', body: '' },
        surgicalTreatement: { title: '', body: '' },
        recovery: { title: '', body: '' },
        sources: ''
      }
    })

    this.fhandler.on('input', () => this.emit('change', this.state.form ) )
  }
  onInput({ data }){ typeof data == 'object' && this.fhandler.fill( data ) }
  onCollapse( section ){ this.state.activeSection = section }
}

<form.py-1 key="catalogue-form">
  <section>
    <div.section-header.px-2.py-1.border-top.cursor-pointer.text-primary on-click('onCollapse', 'about')>About</div>

    <if( state.activeSection === 'about' )>
      <div.section-body.px-2.py-1>
        <div.form-group>
          <label>Description</label>
          <textarea.form-control.form-control-lg.border
                    type="text"
                    name="content.description"
                    value=state.form.content.description
                    style="resize:none;min-height:8rem;"
                    on-change('__onChange')></textarea>
          <if( state.formError['content.description'] )>
            <p.font-small-3.text-warning.mt-0>Description field is required</p>
          </if>
        </div>

        <div.form-group>
          <label>Illustration image</label>
          <input.form-control.form-control-lg.border
                type="text"
                name="content.image"
                value=state.form.content.image
                on-change('__onChange')/>
          <if( state.formError['content.image'] )>
            <p.font-small-3.text-warning.mt-0>Image field is required</p>
          </if>
        </div>
      </div>
    </if>
  </section>

  <section>
    <div.section-header.px-2.py-1.border-top.cursor-pointer.text-primary on-click('onCollapse', 'overview')>Overview</div>

    <if( state.activeSection === 'overview' )>
      <div.section-body.px-2.py-1>
        <div.form-group>
          <textarea.form-control.form-control-lg.border
                    type="text"
                    name="content.overview.body"
                    value=state.form.content.overview.body
                    style="resize:none;min-height:8rem;"
                    on-change('__onChange')></textarea>
        </div>
      </div>
    </if>
  </section>

  <for|key, label| in=SameSet>
    <section>
      <div.section-header.px-2.py-1.border-top.cursor-pointer.text-primary on-click('onCollapse', key )>${label}</div>

      <if( state.activeSection === key )>
        <div.section-body.px-2.py-1>
          <div.form-group>
            <label>Title</label>
            <input.form-control.form-control-lg.border
                  type="text"
                  name=`content.${key}.title`
                  value=state.form.content[ key ].title
                  on-change('__onChange')/>
          </div>

          <div.form-group>
            <label>Body</label>
            <textarea.form-control.form-control-lg.border
                      type="text"
                      name=`content.${key}.body`
                      value=state.form.content[ key ].body
                      style="resize:none;min-height:8rem;"
                      on-change('__onChange')></textarea>
          </div>
        </div>
      </if>
    </section>
  </for>

  <section>
    <div.section-header.px-2.py-1.border-top.cursor-pointer.text-primary on-click('onCollapse', 'sources')>Sources</div>

    <if( state.activeSection === 'sources' )>
      <div.section-body.px-2.py-1>
        <div.form-group>
          <textarea.form-control.form-control-lg.border
                    type="text"
                    name="content.sources"
                    value=state.form.content.sources
                    style="resize:none;min-height:8rem;"
                    on-change('__onChange')></textarea>
        </div>
      </div>
    </if>
  </section>
</form>