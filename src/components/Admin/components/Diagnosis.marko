class {
  onCreate(){ this.state = { collapsed: false } }
  onInput({ active }){ this.setState('collapsed', active ) }
  onCollapse(){
    this.state.collapsed = !this.state.collapsed
    this.state.collapsed && this.emit('active')
  }
}

<li.nav-item.border-bottom.py-1.cursor-pointer>
  <div on-click('onCollapse')>
    <Bx.font-medium-5.px-75.float-right.cursor-pointer 
        type=`chevron-${!state.collapsed ? 'down' : 'up'}`/>
    <span.font-medium-1.black-text.pr-5>${input.name}</span>
  </div>

  <if( state.collapsed )>
    <div.py-1.animated.fadeIn.fast>
      <p>${input.description}</p>
      
      <if( input.causes )>
        <label.font-small-2.text-muted>Causes</label>
        <p></p>
      </if>

      <br>
      <Row.m-0>
        <@col.col.pl-0>
          <button.btn.btn-primary.btn-block.rounded-lg
                  on-click( () => component.emit('edit') )>Edit details</button>
        </@col>
        <@col.col.pl-0>
          <button.btn.btn-outline-danger.btn-block.rounded-lg
                  on-click( () => component.emit('delete') )>Delete</button>
        </@col>
      </Row>
    </div>
  </if>
</li>