.parent-drawflow {
  display: flex;
  overflow: hidden;
  touch-action: none;
  outline:none;
}

.drawflow {
  width: 100%;
  height: 100%;
  position: relative;
  user-select: none;
  perspective: 0;
  /* background-color: red; */
  /* overflow: auto; */
}

.drawflow .parent-node {
  position: relative;

}

.drawflow .drawflow-node {
  display: flex;
  align-items: center;
  position: absolute;
  background: white;
  width: 160px;
  min-height: 40px;
  border-radius:4px;
  border: 1px solid rgba(200, 200, 200);
  color: black;
  z-index: 2;
  padding: 15px;
}

.drawflow .drawflow-node.selected {
  background: rgb(204, 251, 213);
}
.drawflow .drawflow-node:hover {
  cursor: move;
}

.drawflow .drawflow-node .inputs, .drawflow .drawflow-node .outputs {
  width: 0px;
}

.drawflow .drawflow-node .drawflow_content_node {
  width: 100%;
  display: block;
}

.drawflow .drawflow-node .input, .drawflow .drawflow-node .output {

  position: relative;
  width: 15px;
  height: 15px;
  background: white;
  border-radius: 50%;
  border: 1px solid rgba(200, 200, 200);
  cursor: crosshair;
  z-index: 1;
  margin-bottom: 5px;
}

.drawflow .drawflow-node .input {
  left: -23px;
  top: 2px;
  background: rgb(85, 204, 240);
}
.drawflow .drawflow-node .output {
  right: -8px;
  top: 2px;
}

.drawflow svg {
  z-index: 0;
  position: absolute;
  overflow: visible !important;
}
.drawflow .connection {
  position: absolute;
  pointer-events: none;
  aspect-ratio: 1 / 1;
}
.drawflow .connection .main-path {
  fill: none;
  stroke-width: 3px;
  stroke: rgba(167, 192, 212, 0.429);
  pointer-events: all;
}
.drawflow .connection .main-path:hover {
  stroke: #1266ab;
  cursor: pointer;
}

.drawflow .connection .main-path.selected {
  stroke: #43b993;
}

.drawflow .connection .point {
  cursor: move;
  stroke: black;
  stroke-width: 2;
  fill: white;
  pointer-events: all;
}

.drawflow .connection .point.selected, .drawflow .connection .point:hover {
  fill: #1266ab;
}

.drawflow .main-path {
  fill: none;
  stroke-width: 5px;
  stroke: steelblue;
}

.drawflow-delete {
  position: absolute;
  display: block;
  width: 30px;
  height: 30px;
  background: black;
  color: white;
  z-index: 4;
  border: 2px solid white;
  line-height: 30px;
  font-weight: bold;
  text-align: center;
  border-radius: 50%;
  font-family: monospace;
  cursor: pointer;
}
.drawflow > .drawflow-delete {
  margin-left: -15px;
  margin-top: 15px;
}

.parent-node .drawflow-delete {
  right: -15px;
  top: -15px;
}
