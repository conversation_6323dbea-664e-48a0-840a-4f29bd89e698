
class {
  onCreate(){ this.state = { active: false } }
  onActive( dataset ){
    this.state.active = dataset.attribute
    this.emit('map', dataset )
  }
}

<ul.list-unstyled>
  <for|dataset, index| of=input.list>
    <Diagnosis key=index
                ...dataset
                active=(dataset.attribute === state.active)
                on-active('onActive', dataset )
                on-edit( () => component.emit('action', dataset, 'edit' ) )
                on-delete( () => component.emit('delete', dataset.attribute ) )/>
  </for> 
</ul>