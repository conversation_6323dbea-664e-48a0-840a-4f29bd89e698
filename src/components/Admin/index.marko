
import BodyParts from '../../data/bodyParts.json'
import Swal from '~/js/sweetalert2.min'

static const dialog = ( options, callback, dismissCallback ) => {
  
  const defaultOptions = {
    type: 'warning',
    title: 'Are you sure?',
    html: 'You won\'t be able to revert this!',
    showCancelButton: true,
    confirmButtonText: 'Yes',
    confirmButtonClass: 'btn btn-primary mx-25',
    cancelButtonClass: 'btn btn-secondary mx-25'
  }

  Swal.fire({ ...defaultOptions, ...options })
      .then( ({ value, dismiss }) => {
        if( dismiss && typeof dismissCallback == 'function' ) 
          return dismissCallback()

        value && typeof callback == 'function' && callback()
      } )
}

class {
  onCreate(){
    this.state = {
      activeRef: null,
      activePart: null,
      activeDiagnosis: null,
      activeTab: 'questions',
      activeVersion: null,
      physician: null,
      versions: [],
      questions: [],
      diagnosis: [],
      searchResults: null,
      showForm: false,
      alert: false,
      hasUpdate: false,
      staging: true,
      publishing: false
    }
  }
  onMount(){ this.loadVersions() }
  
  onSelectTab( attr ){
    this.state.activeTab = attr
    this.state.activeDiagnosis = null

    this.fetchList()
  }
  onShowForm( dataset, action ){
    this.state.showForm = dataset ? { dataset, action } : false
    this.state.activeRef = dataset.reference
  }
  onHandleSelect( { question, answers, key }, aref ){
    // Convert answers array to object
    if( Array.isArray( answers ) ){
      const _answers = {}
      answers.map( ({ reference, type, value, references }) => {
        _answers[ reference ] = {
          type,
          value,
          references
        }
      } )
      answers = _answers
    }
    
    const action = question == 'New question' ? 'add' : 'edit'

    // Edit selected question
    this.onSelectTab('questions')
    this.onShowForm({ question, answers, reference: key, aref }, action )
  }
  onShowAlert( message ){
    this.state.alert = message
    setTimeout( () => this.state.alert = false, 8000 )
  }
  onMapPath( dataset ){ this.state.activeDiagnosis = dataset }

  onAuth( physician ){ this.state.physician = physician }
  onSearch( value ){
    if( value === false ){
      this.state.searchResults = null
      return
    }

    this.state.searchResults = []
    this.state[ this.state.activeTab ].map( each => {
      each.parts.includes( this.state.activePart )
      && new RegExp( value.replace('+', ' '), 'i' ).test( each.question || each.name )
      && this.state.searchResults.push( each )
    } )

    this.setStateDirty('searchResults')
  }

  async loadVersions(){
    try {
      const { error, message, versions } = await ( await window.fetch(`/versions`) ).json()
      if( error ) throw new Error( message )

      if( !Array.isArray( versions ) || !versions ) return
      this.state.versions = versions

      // Stag the latest recorded version
      const { version, isDraft, published } = versions.slice(-1)[0]
      this.stageVersion({ value: version, isDraft, isPublished: !!published })
    }
    catch( error ){ console.log(`Load versions failed: `, error ) }
  }
  async stageVersion({ value, isDraft, isPublished }){
    this.state.staging = true
    try {
      const { error, message } = await ( await window.fetch(`/versions/${value}/stage`) ).json()
      if( error ) throw new Error( message )
      
      this.state.activeVersion = { value, isDraft, isPublished }
      this.fetchList()
    }
    catch( error ){ console.log(`Stage version ${value} failed: `, error ) }
    this.state.staging = false
  }

  async onSelectPart( attr ){
    this.state.activePart = attr
    this.state.activeDiagnosis = null

    await this.fetchList()

    // Load questions alongside diagnosis when body part change
    this.state.activeTab == 'diagnosis' 
    && await this.fetchList('questions')
  }
  async fetchList( activeTab ){
    if( !this.state.activePart ) return

    activeTab = activeTab || this.state.activeTab
    
    this.state[ activeTab ] = null
    try {
      const { error, message, results } = await ( await window.fetch(`/${activeTab}?parts=${this.state.activePart}`) ).json()
      if( error ) throw new Error( message )

      this.state[ activeTab ] = results
    }
    catch( error ){ console.log(`[SC] - Failed fetching ${activeTab} list: `, error ) }
  }

  async onElementCreate( dataset, callback ){
    try {
      // Remove reference assigned by flowchart
      delete dataset.reference

      const
      options = {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify( dataset )
      },
      { error, message } = await ( await window.fetch(`/${this.state.activeTab}/add`, options ) ).json()
      if( error ) throw new Error( message )

      // New update record flag
      this.state.hasUpdate = true

      this.onShowAlert('Saved!')
      this.fetchList() // Refresh list
      
      setTimeout( () => this.onShowForm( false ), 1000 )
    }
    catch( error ){ console.log(`[SC] - ${this.state.activeTab} create request failed: `, error ) }

    typeof callback == 'function' && callback()
  }
  async onElementUpdate( dataset, callback ){
    try {
      const
      url = this.state.activeTab == 'questions' ?
                            `/questions/${encodeURIComponent( this.state.showForm.dataset.reference )}` // Update question
                            : `/diagnosis/update?name=${encodeURIComponent( this.state.showForm.dataset.name )}`, // Update diagnosis
      options = {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify( dataset )
      },
      { error, message } = await ( await window.fetch( url, options ) ).json()
      if( error ) throw new Error( message )

      // New update record flag
      this.state.hasUpdate = true
      this.state.showForm.dataset = dataset

      this.onShowAlert('Saved!')
      this.fetchList() // Refresh list
    }
    catch( error ){ console.log(`[SC] - ${this.state.activeTab} update request failed: `, error ) }

    typeof callback == 'function' && callback()
  }
  async onElementDelete( reference ){
    dialog( { html: `You want to delete this ${this.state.activeTab}. This action is irreversible!` }, ( async function(){
      try {
        const
        url = this.state.activeTab == 'questions' ?
                              `/questions/${encodeURIComponent( reference )}` // Delete question
                              : `/diagnosis/${encodeURIComponent( reference )}/${this.state.activePart}`, // Delete diagnosis
        options = { method: 'DELETE' },
        { error, message } = await ( await window.fetch( url, options ) ).json()
        if( error ) throw new Error( message )

        // New update record flag
        this.state.hasUpdate = true
        
        this.onShowAlert('Deleted!')
        this.fetchList() // Refresh list
      }
      catch( error ){ console.log(`[SC] - ${this.state.activeTab} delete request failed: `, error ) }
    } ).bind(this) )
  }

  async onLinkQuestion({ from, fromPort, to }){
    // console.log('Link Question:  ', from, fromPort, to )
    if( !fromPort || !to ) return
    try {
      const
      options = {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ from: fromPort, to })
      },
      { error, message } = await ( await window.fetch('/questions/link', options ) ).json()
      if( error ) throw new Error( message )

      // New update record flag
      this.state.hasUpdate = true
      
      this.onShowAlert('Question linked!')
      this.fetchList() // Refresh list
    }
    catch( error ){ console.log(`[SC] - ${this.state.activeTab} linking request failed: `, error ) }
  }
  async onRelinkQuestions( oldLink, { fromPort, to }){
    if( !fromPort || !to ) return
    try {
      const 
      options = {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ from: fromPort, to, current: oldLink.to })
      },
      { error, message } = await ( await window.fetch('/questions/relink', options ) ).json()
      if( error ) throw new Error( message )

      // New update record flag
      this.state.hasUpdate = true
      
      this.onShowAlert('Question relinked!')
      this.fetchList() // Refresh list
    }
    catch( error ){ console.log(`[SC] - ${this.state.activeTab} relinking request failed: `, error ) }
  }
  async onDeleteElements( list ){
    if( !list || !list.length ) return
    try {
      const 
      options = {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify( list )
      },
      { error, message } = await ( await window.fetch('/questions/unlink', options ) ).json()
      if( error ) throw new Error( message )

      // New update record flag
      this.state.hasUpdate = true
      
      this.onShowAlert('Questions/Links deleted!')
      this.fetchList() // Refresh list
    }
    catch( error ){ console.log(`[SC] - ${this.state.activeTab} deleting request failed: `, error ) }
  }
  async onPathCorrection({ type, refs }){
    if( !refs || !refs.length || !this.state.activeDiagnosis ) return
    try {
      const
      options = {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify( refs )
      },
      { error, message, diagnosis } = await ( await window.fetch(`/diagnosis/${this.state.activeDiagnosis.attribute}/path`, options ) ).json()
      if( error ) throw new Error( message )

      // New update record flag
      this.state.hasUpdate = true
      
      this.onShowAlert('Diagnosis path fixed!')
      // this.fetchList() // Refresh list

      console.log( diagnosis )

      // Populate updated path of active diagnosis
      this.onMapPath({ ...(this.state.activeDiagnosis), path: diagnosis.path })
    }
    catch( error ){ console.log(`[SC] - ${this.state.activeTab} path fixing request failed: `, error ) }
  }
  async onSave(){
    if( !this.state.activeVersion || !this.state.hasUpdate ) return
    try {
      const
      v = this.state.activeVersion.isDraft ? this.state.activeVersion.value : '*',
      options = {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ author: this.state.physician.name })
      },
      { error, message } = await ( await window.fetch(`/versions/${v}/draft`, options ) ).json()
      if( error ) throw new Error( message )
      
      this.state.hasUpdate = false
      this.loadVersions()
    }
    catch( error ){ console.log(`[SC] - Save draft version failed: `, error ) }
  }
async onCatalogueUpsert(data) {
  if (typeof data !== 'object' || !data.attribute) {
    return;
  }
  try {
    const
    options = {
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify( data )
    };
    const response = await window.fetch(`/catalogue/${data.attribute}`, options);
    const jsonResponse = await response.json();
    if (jsonResponse.error) {
      throw new Error(jsonResponse.message);
    }
    this.state.hasUpdate = false;
    this.loadVersions();
  } 
  catch( error ){ console.log(`[SC] - Upserting diagnosis catalogue failed: `, error ) }
}
  async onPublish(){
    if( !this.state.activeVersion ) return

    this.state.publishing = true
    try {
      let v = this.state.activeVersion.isDraft ? this.state.activeVersion.value : '*'

      // Republish old version
      if( !this.state.hasUpdate
          && !this.state.activeVersion.isDraft 
          && !this.state.activeVersion.isPublished )
        v = this.state.activeVersion.value
      
      const
      options = {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ author: this.state.physician.name })
      },
      { error, message } = await ( await window.fetch(`/versions/${v}/publish`, options ) ).json()
      if( error ) throw new Error( message )
      
      this.state.hasUpdate = false
      this.loadVersions()
    }
    catch( error ){ console.log(`[SC] - Publish version failed: `, error ) }
    this.state.publishing = false
  }
}

<macro name="QuestionsList">
  <ul.list-unstyled>
    <for|dataset, index| of=(state.searchResults || state.questions)>
      <Question key=index
                ...dataset
                active=(dataset.reference == state.activeRef)
                on-action('onShowForm', dataset )
                on-delete('onElementDelete', dataset.reference )/>
    </for> 
  </ul>
</macro>

<if( state.alert )>
  <div.position-fixed.right-0.mx-2.my-5.round.shadow-lg.bg-success.white-text.px-3.py-1 style="top:50%">${state.alert}</div>
</if>

<if( state.staging || state.publishing )>
  <div.position-fixed.w-100.h-100.top-0.d-flex.align-items-center style="z-index:11;background-color: rgba(255, 255, 255, .6)">
    <div.w-100.text-center>
      <Preloader.font-large-3 active/>

      <p.font-medium-5.my-3>${state.publishing ? 'Publishing' : 'Staging version'}...</p>
    </div>
  </div>
</if>

<Session type="physician" on-auth('onAuth')/>

<if( state.physician )>
  <div.row.m-0>
    <div.col-4.vh-100>
      <Row.position-relative.zindex-4.h-100.bg-white.p-0 class=(!state.showForm ? 'shadow-lg' : false)>
        <@col.col-4.h-100.d-flex.align-items-center>
          <ul.list-unstyled.text-center.px-1>
            <for|{ value, label }, index| of=BodyParts>
              <li.brder.border-2.shadow.rounded-lg.p-1.my-50.cursor-pointer
                  class=(state.activePart == value ? 'bg-primary white-text' : false)
                  key=index
                  on-click('onSelectPart', value)>
                <!-- <img src=""/>-->
                <div>${label}</div>
              </li>
            </for>
          </ul>
        </@col>
        <@col.col-8.h-100.px-0>
          <if( state.activePart )>
            <Row.d-flex.align-items-center>
              <@col>
                <div.tab.text-center.p-75.mx-1.cursor-pointer
                    class=(state.activeTab == 'questions' ? 'border-bottom border-2 border-color-primary' : false)
                    on-click('onSelectTab', 'questions')>Questions</div>
              </@col>
              <@col>
                <div.tab.text-center.p-75.mx-1.cursor-pointer
                    class=(state.activeTab == 'diagnosis' ? 'border-bottom border-2 border-color-primary' : false)
                    on-click('onSelectTab', 'diagnosis')>Diagnosis</div>
              </@col>
            </Row>

            <div.mx-1.py-50.bg-white>
              <SearchBar placeholder=`Search ${state.activeTab}...`
                          on-query('onSearch')
                          on-no-query('onSearch', false )/>
              <br>
              <button.btn.btn-primary.btn-block on-click('onShowForm', {}, 'add')>
                Add new ${state.activeTab}
              </button>
            </div>
          </if>

          <div.overflow-auto.py-50.pl-5 style="height: calc(100% - 12rem)">
            <if( !Array.isArray( state[ state.activeTab ] ) )>
              <div.h-100.d-flex.align-items-center>
                <div.w-100.text-center>
                  <Preloader.font-large-1 active/>
                </div>
              </div>
            </if>
            <else-if( !state[ state.activeTab ].length )>
              <div.h-100.d-flex.align-items-center>
                <div.w-100.text-center>
                  <Bx.font-large-2 type="body"/><br><br>
                  <p>Select a body part</p>
                </div>
              </div>
            </else-if>
            <else>
              <Switch by=state.activeTab>
                <@case is="diagnosis">
                  <DiagnosisList list=(state.searchResults || state.diagnosis)
                                  on-map('onMapPath')
                                  on-action('onShowForm')
                                  on-delete('onElementDelete')/>
                </@case>
                <@case is="questions"><QuestionsList/></@case>
                <@default><QuestionsList/></@default>
              </Switch>
            </else>
          </div>
        </@col>
      </Row>

      <!-- Create/Edit/Link forms -->
      <Switch by=state.activeTab>
        <@case is="diagnosis">
          <DiagnosisForm ...state.showForm
                          activePart=state.activePart
                          on-add('onElementCreate')
                          on-edit('onElementUpdate')
                          on-link('onElementLink')
                          on-upsert-catalogue('onCatalogueUpsert')
                          on-dismiss('onShowForm', false )/>
        </@case>
        <@case is="questions">
          <QuestionForm ...state.showForm
                        activePart=state.activePart
                        on-add('onElementCreate')
                        on-edit('onElementUpdate')
                        on-link('onElementLink')
                        on-dismiss('onShowForm', false )/>
        </@case>
      </Switch>
    </div>
    <div.col-8.vh-100.p-0.theme-bg-fade>
      <div.mx-50.position-absolute.zindex-2.w-100>
        <ul.list-inline.d-inline-flex.px-3.py-1.float-right>
          <!-- <li.mr-1>
            <button.btn.btn-white.btn-icon.rounded-circle.shadow-lg>
              <Bx.font-large-1 type="undo"/>
            </button>
          </li>
          <li.mr-1>
            <button.btn.btn-white.btn-icon.rounded-circle.shadow-lg>
              <Bx.font-large-1 type="redo"/>
            </button>
          </li> -->

          <if( state.hasUpdate || state.activeVersion && ( state.activeVersion.isDraft || !state.activeVersion.isPublished ) )>
            <li.mx-50>
              <button.btn.btn-primary.btn-lg.btn-block.round.shadow-lg.text-nowrap
                      on-click('onPublish')>Publish</button>
            </li>
          </if>

          <if( state.hasUpdate )>
            <li.mx-50>
              <button.btn.btn-outline-primary.btn-lg.btn-block.bg-white.round.shadow-lg.text-nowrap
                      on-click('onSave')>Save draft</button>
            </li>
          </if>

          <li.mx-50>
            <Select.round.bg-white.px-1
                    size="lg"
                    returnAll
                    style="width:18rem"
                    placeholder="No version"
                    value=(state.activeVersion && state.activeVersion.value)
                    options=state.versions.map( ({ version, isDraft, published }) => {
                      return {
                        value: version,
                        label: `Version ${version} - ${isDraft ? 'Draft' : !!published ? 'Published' : '-'}`,
                        isDraft,
                        isPublished: !!published
                      }
                    })
                    on-select('stageVersion')/>
          </li>
        </ul>
      </div>

      <div.h-100>
        <JointChart part=state.activePart
                    path=(state.activeDiagnosis && state.activeDiagnosis.path)
                    questions=state.questions
                    editable=(state.activeTab == 'questions')
                    on-link('onLinkQuestion')
                    on-delete('onDeleteElements')
                    on-relink('onRelinkQuestions')
                    on-select-answer('onHandleSelect')
                    on-select-question('onHandleSelect')
                    on-apply-correction('onPathCorrection')/>
      </div>
    </div>
  </div>
</if>