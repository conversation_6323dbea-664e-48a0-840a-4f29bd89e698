import <PERSON><PERSON>andler from 'markojs-form'
import Datepicker from 'vanillajs-datepicker/Datepicker'
import 'root/../node_modules/vanillajs-datepicker/dist/css/datepicker.css'
import Physicians from '../../../data/physicians.json'

class {
  onCreate(){
    this.state = { session: Store.get('session') }

    this.fhandler = new FormHandler({ key: 'auth', autosave: true, crosscheck: true })
    this.fhandler.bind(this)

    this.format = 'mm/dd/yyyy'
  }
  // onMount(){ this.pickdate() }
	pickdate(){
		setTimeout( () => {
			const 
      elem = this.getEl('pickadate'),
      datepicker = new Datepicker( elem, { format: this.format })
      elem.addEventListener('changeDate', () => {
        this.fhandler.set('dob', datepicker.getDate( this.format ) )
      } )
		}, 200 )
	}
  onSubmitPhysician(){
    const { name, password } = this.state.form;

    if (!name) {
      return this.fhandler.error('name', true);
    }

    if (!password) {
      return this.fhandler.error('password', true);
    }

    let physician = null;
    for (let i = 0; i < Physicians.length; i++) {
      if (Physicians[i].name.toLowerCase() === name.toLowerCase() && Physicians[i].password === password) {
        physician = Physicians[i];
        break;
      }
    }

    if (!physician) {
      return this.fhandler.error('name', 'Unknown physician or incorrect password. Check the details and retry');
    }

    this.emit('submit', { name: physician.name });
  }
  onSubmitPatient(){
    if( !this.state.form.agree_terms ) return

    if( !this.state.form.name ) 
      return this.fhandler.error('name', true )

    if( this.state.form.name.length < 3 ) 
      return this.fhandler.error('name', 'Minimun 3 initials')
    // if( !this.state.form.dob ) return this.fhandler.error('dob', true )
    
    this.emit('submit', {
      name: this.state.form.name,
      // dob: this.state.form.dob
    })
  }
}

<div.position-fixed.zindex-3.vw-100.vh-100.bg-white.d-flex.align-items-center.justify-content-center>
  <div.col-lg-4.col-md-6.col-sm-8.col-10 key="auth">
    <div.w-100.text-center style="margin-bottom:6rem">
      <img src=require( window.CLIENT_ID ? `~/tenant/po.png` : `~/logo.png`) style="width:60%"/>
    </div>
    
    <form.w-100.shadow-lg.p-5.round.bg-white key="auth">
      <if( input.as == 'physician' )>
        <span.font-medium-3>Physician's fullname</span>
        <div.form-group.mt-2>
          <input.form-control.form-control-lg
                type="text"
                name="name"
                value=state.form.name
                on-change('__onChange')/>
          <span.font-medium-3>Password</span>     
          <input.form-control.form-control-lg
                type="password"
                name="password"
                value=state.form.password
                on-change('__onChange')/>
          <if(state.formError.name)>
            <p.mt-2.font-small-4.text-primary>
              ${typeof state.formError.name == 'string' ? state.formError.name : 'Your first & last name are required'}
            </p>
          </if>
          <if(state.formError.password)>
            <p.mt-2.font-small-4.text-primary>
              ${typeof state.formError.password == 'string' ? state.formError.password : 'Password is required'}
            </p>
          </if>
        </div>
        
        <br>
        <button.btn.btn-primary.btn-block.btn-lg
                type="submit"
                disabled=(!state.form.name || !state.form.password)
                on-click('onSubmitPhysician')>Continue</button>
      </if>
      
      <else>
        <div.form-group>
          <span.font-medium-3>Patient Initials</span>
          <input.form-control.form-control-lg.mt-2
                type="text"
                name="name"
                min=3
                value=state.form.name
                placeholder="Eg. JFK"
                on-change('__onChange')/>
          <if( state.formError.name )>
            <p.mt-2.font-small-4.text-primary>
              ${typeof state.formError.name == 'string' ? state.formError.name : 'Name Initials are required'}
            </p>
          </if>
        </div>
        
        <!-- <div.form-group>
          <span.font-medium-3>Date of Birth</span>
          <input.form-control.form-control-lg.mt-2.pickadate.position-relative
                  type="text"
                  key="pickadate"
                  placeholder=component.format
                  value=state.form.dob/>
          <if( state.formError.dob )>
            <p.mt-2.font-small-4.text-primary>Date of birth is required</p>
          </if>
        </div> -->

        <!-- Terms & Conditions Checkbox -->
        <div class="mb-1">
          <div class="checkbox checkbox-primary">
            <input type="checkbox"
                    no-update
                    id="agree-terms"
                    name="agree_terms"
                    checked=state.form.agree_terms
                    on-change('__onChecked')/>
            <label for="agree-terms" class="font-medium-1">
              I agree to the 
              <a href="https://upswinghealth.com/terms-and-conditions/" target="_blank">terms of user </a> and 
              <a href="https://upswinghealth.com/privacy-policy/" target="_blank">privacy policy</a>
            </label>
          </div>
        </div>
        
        <br>
        <button.btn.btn-primary.btn-block.btn-lg
                type="submit"
                disabled=(!state.form.name || !state.form.agree_terms)
                on-click('onSubmitPatient')>Continue</button>
      </else>
    </form>
  </div>
</div>