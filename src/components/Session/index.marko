import dummyPath from "../../components/Assessment/path.json";
class {
  onCreate({ type }) {
    this.state = {
      session: Store.get(`session-${type}`),
      greeting: false,
    };
  }
  onMount() {
    if (
      !this.state.session ||
      !this.state.session.name ||
      this.state.session.dob
    ) {
      Store.clear(`session-${this.input.type}`);
      return;
    }

    this.state.session.name && this.greet();
    Store.set(`session-${this.input.type}`, {
      ...this.state.session,
      connectionCount: ++this.state.session.connectionCount,
    });

    this.emit("auth", this.state.session);
  }
  onAuth({ name, dob }) {
    this.state.session = { name, dob, connectionCount: 1 };

    name && this.greet();
    Store.set(`session-${this.input.type}`, this.state.session);

    this.emit("auth", this.state.session);
  }
  onClose() {
    this.state.session = false;
    Store.clear(`session-${this.input.type}`);

    this.emit("auth", null);
  }
  greet() {
    this.state.greeting = true;
    setTimeout(() => (this.state.greeting = false), 6000);
  }
}

<if(!state.session)>
  <Auth as=input.type on-submit("onAuth")/>
</if>
<else>
  <div
    class=[
      "position-fixed p-50 bottom-0 round bg-white shadow-lg cursor-default d-flex align-items-center justify-content-between",
      input.position ? input.position + "-0" : "left-0",
    ]
    style="z-index:10;margin:2.5rem"
  >
    $ const { name, connectionCount } = state.session;

    <if(state.greeting)>
      <span class="font-medium-3 black-text mx-1">
        $ const firstName = name.split(/\s+/)[0];

        <if(connectionCount > 1)>
          Welcome back, ${firstName}!
        </if>
        <else>Hi, ${firstName}!</else>
      </span>
    </if>
    <else>
      <div class="font-medium-2 mx-1">
        <if(input.type == "physician")>
          Dr.
        </if>
        ${name}
      </div>
    </else>

    <div
      style="margin-left:15px"
      on-click("onClose")
      class="px-1 py-50 cursor-pointer round bg-primary white-text font-small-2"
    >
      Close
    </div>
  </div>
</else>
