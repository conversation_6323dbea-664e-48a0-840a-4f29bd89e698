import Questions from '../../../data/questionnaires.json'
import { includeObject } from '../../../utils'

class {
  onCreate(){
    this.state = {
      question: '',
      questionSuggestions: null,
      answers: [{ value: '', references: [] }], // Default
      linkQuestion: false,

      alert: false,
      linkingIndex: null,
      changeLinkToQuestion: false
    }
  }

  onInputQuestion( value ){
    if( value === false ){
      this.state.question = ''
      this.state.questionSuggestions = []
      return
    }
    
    this.state.question = value
    this.state.questionSuggestions = []
    this.setStateDirty('questionSuggestions')
    
    let counter = 0
    for( const ref in Questions ){
      const { question, parts } = Questions[ ref ]

      if( !new RegExp( value.replace('+', ' '), 'i' ).test( question ) ) continue

      const sug = {
        title: question,
        // subtitle: parts.join(', ')
      }

      !includeObject( this.state.questionSuggestions, sug, ['title'] )
      && this.state.questionSuggestions.push( sug )

      counter++
      if( counter == 5 ) break
    }

    this.setStateDirty('questionSuggestions')
  }
  onSelectSuggestion( index ){
    this.setState({
      question: this.state.questionSuggestions[ index ].title,
      questionSuggestions: null
    })
  }
  onAddAnswer(){
    this.state.answers.push({ value: '', references: [] })
    this.setStateDirty('answers')
  }
  onAnswerInput( index, e ){
    this.state.answers[ index ].value = e.target.value 
    this.setStateDirty('answers')
  }

  onLinkAnswer( index ){
    const dismiss = index === null

    if( !dismiss && !this.state.answers[ index ].value )
      return

    this.state.linkQuestion = false
    this.state.linkingIndex = index
    if( dismiss ) 
      this.state.changeLinkToQuestion = false

    const $container = $('#linking-container')
    $container.animate({ scrollLeft: dismiss ? 0 : $container.children().eq(0).width() })
  }
  onRemoveAnswer( index ){
    this.state.answers.splice( index, 1 )
    this.setStateDirty('answers')
  }
  onFindLinkQuestion( value ){
    if( value === false ){
      this.state.questionSuggestions = []
      return
    }
    
    this.state.questionSuggestions = []
    this.setStateDirty('questionSuggestions')
    
    let counter = 0
    for( const ref in Questions ){
      const { question, parts, reference } = Questions[ ref ]

      if( !parts.filter( each => { return this.input.parts.includes( each ) } ).length
          || !new RegExp( value.replace('+', ' '), 'i' ).test( question ) )
        continue

      const sug = {
        title: question,
        subtitle: parts.join(', '),
        reference
      }

      !includeObject( this.state.questionSuggestions, sug, ['title'] )
      && this.state.questionSuggestions.push( sug )

      counter++
      if( counter == 5 ) break
    }

    this.setStateDirty('questionSuggestions')
  }
  onSelectLinkQuestion( index ){
    this.setState({
      linkQuestion: this.state.questionSuggestions[ index ],
      questionSuggestions: null
    })

    this.onChangeLinkQuestion( false )
  }
  onChangeLinkQuestion( status ){ this.state.changeLinkToQuestion = status }
  onApplyLink(){
    if( this.state.linkingIndex === null ) return
    
    const reference = this.state.linkQuestion && this.state.linkQuestion.reference || this.input.below.reference

    this.state.answers[ this.state.linkingIndex ].references = [ reference ]
    this.setStateDirty('answers')
    
    this.onLinkAnswer( null )
  }

  isValid(){
    return !!(this.state.question && this.state.answers.filter( ({ value, references }) => {
                return value && references.length 
              } ).length)
  }
  formatQuestion( reference ){

    const _qset = {
      reference,
      entrypoint: false,
      parts: this.input.parts,
      type: 'single_answer',
      question: this.state.question,
      answers: {},
      parents: []
    }
    
    this.state.answers.map( ({ value, references }, index ) => {
      _qset.answers[`${reference}:${index + 1}`] = { type: 'unique', value, references }
    } )

    return _qset
  }
  
  async getLastReference(){
    try {
      const { error, message, reference } = await ( await window.fetch(`/questions/lastref`, { method: 'GET' }) ).json()
      if( error ) throw new Error( message )

      // Add 0 every new generated reference to avoid conflit with existing references
      return '0'+( Number( reference ) + 1 )
    }
    catch( error ){ console.log('Failed getting last reference: ', error ) }
  }
  async saveQuestion(){
    if( !this.isValid() ) return

    let reference = await this.getLastReference()
    if( !reference ) return
    
    try {
      const
      options = {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify( this.formatQuestion( reference ) )
      },
      { error, message } = await ( await window.fetch(`/questions/add`, options ) ).json()
      if( error ) throw new Error( message )

      await this.alterLinks( reference )

      this.state.alert = 'Question added'
      setTimeout( () => this.emit('dismiss'), 8000 )
    }
    catch( error ){ console.log('Failed saving question: ', error ) }
  }
  async alterLinks( reference ){
    if( !reference ) return
    
    try {
      const
      payload = {
        from: this.input.above.answer.key,
        to: reference,
        current: this.input.below.reference
      },
      options = {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify( payload )
      },
      { error, message } = await ( await window.fetch(`/questions/linking`, options ) ).json()
      if( error ) throw new Error( message )
    }
    catch( error ){ console.log('Failed altering insertion links: ', error ) }
  }
}

<div.position-absolute.h-100.w-100.top-0.d-flex.align-items-center.justify-content-center>
  <div.position-absolute.w-100.h-100
      style="background-color: rgba(50, 50, 50, .7)"
      on-click( () => component.emit('dismiss') )></div>

  <div#linking-container.position-relative.zindex-1.bg-white.shadow-lg.rounded-lg.overflow-auto style="width:85%">
    <Row.no-gutters style="width:200%">
      <@col.col.p-0>
        <if( input.above )>
          <div.px-3.py-1.border-bottom>
            <!-- <label.text-primary.mb-2>Before</label> -->
            <p.m-0.black-text.font-medium-1>${input.above.question}</p>
            <span.text-primary.font-medium-1>— ${input.above.answer.value}</span>
          </div>
        </if>

        <div.px-3.py-2>
          <div.form-group.position-relative.zindex-2>
            <label>New Question</label>
            <SearchBar icon=false
                        placeholder="Enter question"
                        suggestions=state.questionSuggestions
                        on-query('onInputQuestion')
                        on-select('onSelectSuggestion')
                        on-no-query('onInputQuestion', false )/>
          </div>

          <div.form-group>
            <div.d-flex.align-items-center.justify-content-between>
              <label>Possible answers</label>
              <button.btn.btn-primary on-click('onAddAnswer')>Add</button>
            </div>

            <div.border-left.ml-3 style="margin-top:-2rem">
              <for|{ value }, index| of=state.answers>
                <div.position-relative.border-bottom.my-2 style="min-height:3.5rem">
                  <div.position-absolute.bg-white style="width:90%;margin-left:10%;bottom:-1.65rem">
                    <div.input-group>
                      <input.form-control.form-control-lg
                            type="text"
                            placeholder="Answer"
                            value=value
                            on-input('onAnswerInput', index )/>
                      <div.input-group-addon>
                        <button.btn.btn-primary.btn-lg.px-1
                                style="border-radius:0"
                                title="Link to Question"
                                disabled=!value
                                on-click('onLinkAnswer', index )><Bx type="link"/></button>
                        <button.btn.btn-light.btn-lg.px-1 
                                style="border-radius:0"
                                title="Remove"
                                on-click('onRemoveAnswer', index )><Bx type="x"/></button>
                      </div>
                    </div>
                  </div>
                </div>
              </for>
            </div>
          </div>

          <br><br>
          <if( state.alert )>
            <div.text-success.py-1>${state.alert}</div>
          </if>
          <else>
            <button.btn.btn-primary.btn-lg.btn-block
                    disabled=!component.isValid()
                    on-click('saveQuestion')>Save</button>
          </else>
        </div>

        <if( input.below )>
          <div.px-3.py-1.border-top>
            <!-- <label.text-primary.mb-2>After</label> -->
            <p.m-0.black-text.font-medium-1>${input.below.question}</p>
            <!-- <span.text-primary.font-medium-1>— ${input.below.answer.value}</span> -->
          </div>
        </if>
      </@col>

      <@col.col.p-0>
        <div.p-3>
          <if( state.linkingIndex !== null )>
            <p.black-text.font-medium-4><span>Link answer</span> (<span.text-primary>${state.answers[ state.linkingIndex ].value}</span>) to: </p>
            <p.black-text.font-medium-2>
              <!-- Insert a new question -->
              <if( state.changeLinkToQuestion )>
                <label>Find Question</label>
                <SearchBar icon=false
                            placeholder="Type here"
                            suggestions=state.questionSuggestions
                            on-query('onFindLinkQuestion')
                            on-select('onSelectLinkQuestion')
                            on-no-query('onFindLinkQuestion', false )/>
              </if>

              <!-- Display input link question -->
              <else>
                — ${state.linkQuestion ? state.linkQuestion.title : input.below.question}
                <span.pl-4.float-right>
                  <button.btn.btn-primary.btn-sm on-click('onChangeLinkQuestion', true )>Change</button>
                </span>
              </else>
            </p>
            
            <br><br>
            <button.btn.btn-light.m-25 on-click('onLinkAnswer', null )>Cancel</button>
            <button.btn.btn-primary.m-25 on-click('onApplyLink')>Apply</button>
          </if>
        </div>
      </@col>
    </Row>
  </div>
</div>