import moment from 'moment'
import Datepicker from 'vanillajs-datepicker/Datepicker'
import BodyParts from '../../../data/bodyParts.json'
import Physicians from '../../../data/physicians.json'
import 'root/../node_modules/vanillajs-datepicker/dist/css/datepicker.css'

class {
  onCreate(){
    this.state = {
      part: false,
      physician: false,
      reviewed: false,
      from: '',
      to: ''
    }
    
    this.format = 'mm/dd/yyyy'
  }
  onMount(){
    this.pickdate('from')
    this.pickdate('to')
  }
  onUpdate(){
    this.emit('change', this.state.toJSON() )
  }
	pickdate( type ){
		setTimeout( () => {
			const
      elem = this.getEl(`pickadate-${type}`),
      datepicker = new Datepicker( elem, { format: this.format })

      elem.addEventListener('changeDate', () => {
        this.state[ type ] = datepicker.getDate( this.format )
      })
		}, 200 )
	}
}

<div.mt-4 class=(!input.show ? 'd-none' : false)>
  <label.text-primary>Filters</label>

  <!-- Select Body part & physician -->
  <section.form-row.py-50.m-0>
    <div.col.pl-0>
      <label>Body part</label>
      <Select.border.rounded.shadow-none.pl-5.bg-white
              size="lg"
              value=state.part
              options=[{ value: '', label: 'Any' }, ...BodyParts ]
              placeholder="Body part's name"
              on-select( value => component.setState('part', value ) )/>
    </div>
    <div.col.pr-0>
      <label>Physician</label>
      <Select.border.rounded.shadow-none.pl-5.bg-white
              size="lg"
              value=state.physician
              options=[
                { value: '', label: 'Any' },
                ...Physicians.map( name => { return { value: name } } )
              ]
              placeholder="Physician's name"
              on-select( value => component.setState('physician', value ) )/>
    </div>
  </section>
  
  <!-- Date & time range -->
  <section.form-row.py-50.m-0 no-update>
    <div.col.pl-0>
      <label>From</label>
      <input.form-control.form-control-lg.px-1.pickadate.position-relative.rounded
              type="text"
              key="pickadate-from"
              placeholder=component.format
              value=state.from/>
    </div>
    <div.col.pr-0>
      <label>To</label>
      <input.form-control.form-control-lg.px-1.pickadate.position-relative.rounded
              type="text"
              key="pickadate-to"
              placeholder=component.format
              value=state.to/>
    </div>
  </section>

  <!-- Show only reviewed assessments -->
  <br>
  <section.py-50>
    <div.checkbox.checkbox-primary>
      <input type="checkbox"
              id="reviewed"
              checked=state.reviewed
              on-change( e => component.setState('reviewed', e.target.checked ) )/>
      <label.font-medium-1 for="reviewed">Show only reviewed assessments</label>
    </div>
  </section>
</div>