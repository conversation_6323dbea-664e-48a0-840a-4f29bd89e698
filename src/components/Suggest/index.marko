import { includeObject } from '../../utils'
import Diagnosis from '../../data/diagnosis.json'
import Catalogue from '../../data/catalogue.json'

class {
  onCreate(){
    this.state = {
      suggestions: [],
      searchResults: null
    }
  }
  
  onSearchCondition( query ){
    if( query < 4 ) return
    
    this.state.searchResults = []
    const regex = new RegExp( query, 'i' )

    Object.entries( Diagnosis )
          .map( ([ part, list ]) => {
            list.map( condition => {
              if( ( regex.test( condition.name )
                    || regex.test( condition.attribute ) )
                    && !includeObject( this.state.searchResults, condition, ['attribute'] ) )
                this.state.searchResults.push({
                  part,
                  name: condition.name,
                  title: condition.name,
                  attribute: condition.attribute
                })
            })
          })
  }
  onSaveSuggestions(){ this.emit('suggest', this.state.suggestions ) }
  onSelectSuggestion( index ){
    const
    selected = this.state.searchResults[ index ],
    attribute = selected.attribute

    if( !attribute || !Catalogue[ attribute ] ) return

    const condition = { ...selected, ...Catalogue[ attribute ] }
    delete condition.title
    
    if( !includeObject( this.state.suggestions, condition, ['attribute'] ) )
      this.state.suggestions.push( condition )

    this.state.searchResults = null
  }
}

<div.w-100.h-100.overflow-auto>
  <div.position-sticky.top-0.bg-white.p-2>
    <button.btn.btn-outline-primary.btn-lg on-click( () => component.emit('cancel') )>Cancel</button>
    <if( state.suggestions.length )>
      <button.btn.btn-primary.btn-lg.float-right
              on-click( () => component.emit('save', state.suggestions ) )>Save</button>
    </if>
  </div>

  <div.px-2>
    <if( Array.isArray( state.suggestions ) && state.suggestions.length )>
      <div.my-1>
        <for|condition, index| of=state.suggestions>
          <Condition key=index as="best" ...condition/>
        </for>
      </div>
    </if>

    <br>
    <div.form-group style="height:30rem">
      <span.font-medium-2.black-text>Add other possible condition</span>
      <br>
      <SearchBar suggestions=state.searchResults
                  placeholder="Search ..."
                  on-query('onSearchCondition')
                  on-select('onSelectSuggestion')/>
    </div>
  </div>
</div>