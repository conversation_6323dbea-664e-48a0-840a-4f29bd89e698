<div class=(input.inrow ? 'col-lg-4 col-md-6 col-12' : 'py-1')>
  <h3.mt-1.font-large-1.strong>${input.name || input.content.title}</h3>

  <if( input.review )>
    <div.form-row.border.rounded-lg.px-25.py-75>
      <div.col-12.px-1.mb-2.text-primary>Accuracy as ${input.as} condition</div>
      <div.col.radio.radio-primary>
        <input type="radio"
                name=input.attribute
                id=`check-${input.attribute}-high`
                no-update
                on-change( () => component.emit('review', input.as, input.attribute, 'high') )/>
        <label for=`check-${input.attribute}-high`>High</label>
      </div>

      <div.col.radio.radio-primary>
        <input type="radio"
                name=input.attribute
                id=`check-${input.attribute}-medium`
                no-update
                on-change( () => component.emit('review', input.as, input.attribute, 'medium') )/>
        <label for=`check-${input.attribute}-medium`>Medium</label>
      </div>

      <div.col.radio.radio-primary>
        <input type="radio"
                name=input.attribute
                id=`check-${input.attribute}-low`
                no-update
                on-change( () => component.emit('review', input.as, input.attribute, 'low') )/>
        <label for=`check-${input.attribute}-low`>Low</label>
      </div>
    </div>
  </if>
  
  <if( input.content )>
    <br>
    <div.w-100.text-center.bg-white><img src=input.content.image style="height:200px"/></div>
    <br>
    <p.font-medium-2>${input.content.description}</p>
    <!-- <a.btn.btn-outline-primary href=input.link target="_blank">Read More</a> -->
    <button.btn.btn-outline-primary on-click( () => component.emit('read-more') )>Read More</button>
  </if>
</div>