
import moment from 'moment'
import { toCapitalCase } from '../../utils'

class {
  onCreate(){
    this.state = {
      surveys: [],
      staged: null,
      activeIndex: null,
      insertion: null,

      searching: false
    }
  }
  onOpenSurvey( index ){
    this.state.staged = this.state.surveys[ index ]
    if( !this.state.staged ) return

    this.state.activeIndex = index
  }
  onInsertQuestion( index ){
    const
    above = this.state.staged.path[ index ],
    below = this.state.staged.path[ index + 1 ] || null
    
    this.state.insertion = { above, below }
  }
  onDismissInsertion(){ this.state.insertion = null }

  async onSearch( query ){
    if( query === false ){
      this.state.surveys = []
      return
    }

    this.state.surveys = []
    this.state.searching = true
    try {
      const
      options = {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-Tenant-Id': window.location.hostname
        }
      },
      { error, message, results } = await ( await window.fetch(`/surveys/search?q=${query}`, options ) ).json()
      if( error ) throw new Error( message )

      this.setState('surveys', results )
    }
    catch( error ){ console.log('Failed searching survey: ', error ) }
    this.state.searching = false
  }
  async onReviewAccuracy( payload ){
    if( !this.physician || !this.state.staged || !this.state.staged._id ) return
    try {
      const
      options = {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ ...payload, by: this.physician.name })
      },
      { error, message, survey } = await ( await window.fetch(`/surveys/${this.state.staged._id}/review/accuracy`, options ) ).json()
      if( error ) throw new Error( message )

      if( survey )
        this.state.staged = survey
    }
    catch( error ){ console.log('Failed saving survey accuracy: ', error ) }
  }
  async onReviewSuggestions( payload ){
    if( !this.physician || !this.state.staged || !this.state.staged._id ) return
    try {
      const
      options = {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ ...payload, by: this.physician.name })
      },
      { error, message, survey } = await ( await window.fetch(`/surveys/${this.state.staged._id}/review/suggestions`, options ) ).json()
      if( error ) throw new Error( message )

      if( survey )
        this.state.staged = survey
    }
    catch( error ){ console.log('Failed saving survey accuracy: ', error ) }
  }
}

<Session type="physician"
          position="right"
          on-auth( data => component.physician = data )/>

<macro|{ status, by }| name="Review">
  <div.mt-2>
    <span>Review: </span>
    <span class=(status == 'APPROVED' ? 'text-success' : 'text-warning')>${status.toLowerCase()}</span> 
    <span>by </span>
    <span.black-text>${by}</span>
  </div>
</macro>

<macro|{ name, attribute, content, accuracy, as, author, review, suggested }| name="Condition">
  $ const hasAccuracies = Array.isArray( accuracy ) && accuracy.length

  <li.row.py-75.d-flex class=( hasAccuracies || review ? 'align-items-top' : 'align-items-center')>
    <div.col-2.text-center>
      <img.rounded-lg.border.border-2 src=content.image style="width:65px;height:65px"/>
    </div>
    <div.col>
      <p.m-0.black-text.font-medium-4>${name || toCapitalCase( attribute.replace(/-/g, ' ') || content.title )}</p>
      <!-- Auggested condition author -->
      <if( suggested )>
        <div.mt-2>
          <span>Suggested by </span>
          <span.black-text>${author}</span>
        </div>
      </if>

      <!-- Suggestion review -->
      <if( review )>
        <Review ...review/>
      </if>

      <!-- Accuracy rate review -->
      <if( hasAccuracies )>
        <div.mt-2>
          <for|{ value, by, review }, index| of=accuracy>
            <li.row.d-flex.align-items-center.font-medium-1.my-25 key=index>
              <div.col>
                <div>
                  <span>Accuracy rated </span>
                  <Switch by=value>
                    <@case is="high"><span.text-success>High</span></@case>
                    <@case is="medium"><span.text-primary>Medium</span></@case>
                    <@case is="low"><span.text-danger>Low</span></@case>
                  </Switch>
                  <span> by </span>
                  <span.black-text>${by}</span>

                  <if( review )>
                    <span> and </span>
                    <span class=(review.status == 'APPROVED' ? 'text-success' : 'text-warning')>${review.status.toLowerCase()}</span>
                    <span> by </span>
                    <span.black-text>${review.by}</span>
                  </if>
                </div>
              </div>

              <div.col-5.text-right>
                <button.btn.btn-success.btn-sm.m-25 on-click('onReviewAccuracy', { status: 'APPROVED', attribute, as, author: by })>Approve</button>
                <button.btn.btn-outline-dark.btn-sm.m-25 on-click('onReviewAccuracy', { status: 'DISAPPROVED', attribute, as, author: by })>Disapprove</button>
              </div>
            </li>
          </for>
        </div>
      </if>
    </div>

    <!-- Approve/Disaprove suggested conditions -->
    <if( suggested )>
      <div.col-4>
        <button.btn.btn-success.btn-sm.m-25 on-click('onReviewSuggestions', { status: 'APPROVED', attribute, author })>Approve</button>
        <button.btn.btn-outline-dark.btn-sm.m-25 on-click('onReviewSuggestions', { status: 'DISAPPROVED', attribute, author })>Disapprove</button>
      </div>
    </if>
  </li>
</macro>

<Row.no-gutters.m-0>
  <!-- Find & display surveys  -->
  <@col.col-3.vh-100.p-0.overflow-auto>
    <!-- Search survey -->
    <div.position-sticky.top-0.px-2.py-1.bg-white.shadow>
      <p.font-medium-2.strong.black-text>Find surveys by condition name, accuracy, physician, ...</p>
      <SearchBar placeholder="Search ..."
                  on-query('onSearch')
                  on-no-query('onSearch', false )/>
    </div>

    <!-- Display survey search results -->
    <if( Array.isArray( state.surveys ) && state.surveys.length )>
      <ul.list-unstyled.px-2.m-0>
        <for|{ parts, bests, alternatives, suggestions, saved }, index| of=state.surveys>
          <li.px-2.py-1.my-1.shadow.rounded-lg.bg-white.cursor-pointer 
              class=(state.activeIndex === index ? 'border border-3' : false)
              key=index
              on-click('onOpenSurvey', index )>
            <!-- Body parts -->
            <if( Array.isArray( parts ) && parts.length )>
              <ul.list-inline.mb-4>
                <for|part, pindex| of=parts>
                  <li.badge.badge-primary.mx-25 key=pindex>${part}</li>
                </for>
              </ul>
            </if>

            <div.my-25.black-text>(${Array.isArray( bests ) ? bests.length : 0}) Best conditions</div>
            <div.my-25.black-text>(${Array.isArray( alternatives ) ? alternatives.length : 0}) Alternative conditions</div>
            <!-- <div.my-25.black-text>(${Array.isArray( suggestions ) ? suggestions.length : 0}) Suggested conditions</div> -->

            <div.mt-4>${moment( saved.at ).calendar()}</div>
          </li>
        </for>
      </ul>
    </if>

    <!-- Searching -->
    <else-if( state.searching )>
      <div.d-flex.align-items-center style="height:80%">
        <div.w-100.text-center>
          <Preloader.font-large-2 active/><br>
          <p>Searching...</p>
        </div>
      </div>
    </else-if>

    <!-- No item -->
    <else>
      <div.d-flex.align-items-center style="height:75%">
        <div.w-100.text-center>No survey</div>
      </div>
    </else>
  </@col>
  
  <!-- Display survey questionnaires/path -->
  <@col.col-4.vh-100.bg-white.border-left.p-0.overflow-auto>
    <div.position-sticky.top-0.px-3.py-2.bg-white>
      <p.font-medium-2.black-text.strong.font-medium-3>Questionnaires</p>
    </div>

    <if( state.staged && Array.isArray( state.staged.path ) && state.staged.path.length )>
      <if( state.staged.patient )>
        <ul.list-unstyled.px-3>
          <li>
            <span.strong>Patient Name:</span> 
            <span.strong.text-primary>${state.staged.patient.name}</span>
          </li>
          <!-- <li>
            <span.strong>Date or birth:</span> 
            <span.strong.text-primary>${state.staged.patient.dob}</span>
          </li> -->
        </ul>
      </if>

      <ul.list-unstyled.px-3>
        <for|{ question, answer }, index| of=state.staged.path>
          <li.question-item.py-50 key=index>
            <p.m-0.black-text.font-medium-1>${question}</p>
            <span.text-primary.font-medium-1>— ${answer.value}</span>

            <!-- <div.insert.position-relative.py-1.my-50 on-click('onInsertQuestion', index )>
              <div.border-top></div>
              <div.position-absolute.d-flex.align-items-center.shadow.border.round.bg-white.cursor-pointer style="margin-top:-1.4rem;margin-left:-2.5rem">
                <Bx.font-large-1.p-25 type="plus"/>
                <span.d-inline-block.text-nowrap.strong.black-text style="padding-right:1rem">${index < state.staged.path.length ? 'Insert' : 'Add'} Question</span>
              </div>
            </div> -->
          </li>
        </for>
      </ul>
    </if>
    <else>
      <div.d-flex.align-items-center style="height:87%">
        <div.w-100.text-center>Select a survey</div>
      </div>
    </else>

    <!-- Insert new question modal -->
    <!-- <if( state.insertion )>
      <InsertQuestion parts=state.staged.parts
                      ...state.insertion
                      on-dismiss('onDismissInsertion')/>
    </if> -->
  </@col>

  <!-- Dipslay conditions (Bests, alternatives, suggestions) -->
  <@col.col.vh-100.bg-white.border-left.p-0.overflow-auto>
    <div.position-sticky.top-0.px-3.py-2.bg-white>
      <p.font-medium-2.black-text.strong.font-medium-3>Conditions</p>
    </div>

    <if( state.staged )>
      <!-- Best conditions -->
      <div>
        $ const hasBests = Array.isArray( state.staged.bests ) && state.staged.bests.length

        <p.font-medium-2.px-3.py-1.m-0.border-top>
          <span>Bests</span>
          <if( !hasBests )><span.text-danger.px-50>(None)</span></if>
        </p>

        <ul.list-unstyled.px-3.m-0.border-bottom>
          <if( hasBests )>
            <for|condition, index| of=state.staged.bests>
              <Condition key=index 
                          as="best"
                          ...condition/>
            </for>
          </if>
        </ul>
      </div>

      <!-- Alternative conditions -->
      <div>
        $ const hasAlternatives = Array.isArray( state.staged.alternatives ) && state.staged.alternatives.length

        <p.font-medium-2.px-3.py-1.m-0>
          <span>Alternatives</span>
          <if( !hasAlternatives )><span.text-danger.px-50>(None)</span></if>
        </p>

        <ul.list-unstyled.px-3.m-0.border-bottom>
          <if( hasAlternatives )>
            <for|condition, index| of=state.staged.alternatives>
              <Condition key=index 
                          as="alternative"
                          ...condition/>
            </for>
          </if>
        </ul>
      </div>

      <!-- Suggested conditions -->
      <div>
        $ const hasSuggestions = Array.isArray( state.staged.suggestions ) && state.staged.suggestions.length

        <p.font-medium-2.px-3.py-1.m-0>
          <span>Suggestions</span>
          <if( !hasSuggestions )><span.text-danger.px-50>(None)</span></if>
        </p>

        <ul.list-unstyled.px-3.m-0.border-bottom>
          <if( hasSuggestions )>
            <for|{ condition, by, review }, index| of=state.staged.suggestions>
              <Condition suggested 
                          key=index 
                          review=review 
                          author=by
                          ...condition/>
            </for>
          </if>
        </ul>
      </div>
    </if>
    <else>
      <div.d-flex.align-items-center style="height:87%">
        <div.w-100.text-center>Select a survey</div>
      </div>
    </else>
  </@col>
</Row>