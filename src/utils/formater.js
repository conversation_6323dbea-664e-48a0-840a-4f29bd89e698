
const fs = require('fs')

function formatQuestions( part, { nodes, links }){
  const data = {}

  Object
  .entries( nodes )
  .map( ([ reference, value ]) => {
    const { title, question_type } = value.properties
    data[ reference ] = {
      reference,
      entrypoint: title === 'Start',
      parts: [ part ],
      type: question_type,
      question: title,
      answers: {},
      parents: []
    }

    Object
    .entries( value.ports )
    .map( ([ pref, value ]) => {
      if( /^input-/.test( pref ) ) return

      const { title, answer_type } = value.properties
      data[ reference ].answers[`${reference}:${pref}`] = {
        type: answer_type == 'answer' ? 'unique' : answer_type,
        value: title,
        references: []
      }
    } )
  } )

  Object
  .values( links )
  .map( ({ from, to }) => {
    const 
    fromref = from.nodeId,
    toref = to.nodeId,
    aref = `${fromref}:${from.portId}`

    data[ fromref ].answers[ aref ].references.push( toref )
    data[ toref ].parents.push( aref )
  } )

  return data
}
function formatDiagnosis( part, list ){
  if( !Array.isArray( list ) ) return {}

  const data = []

  function mapReferences( ids ){
    const Refs = []

    for( const x in ids ){
      const id = ids[x]
      if( id === 'undefined' || !QUESTIONNAIRES ) continue

      // Refs.push( id )

      Object
      .values( QUESTIONNAIRES )
      .map( ({ answers }) => {
        Object.keys( answers )
              .map( reference => new RegExp(`:${id}$`).test( reference ) && Refs.push( reference ) )
      })
    }

    return Refs
  }

  list.map( ({ body_part_slug, title, answer_ids, slug }, index ) => {

    if( body_part_slug === part || ( answer_ids && answer_ids.length && body_part_slug != 'misc' ) )
      data.push({
        parts: [ part ],
        name: title,
        attribute: slug,
        path: mapReferences( answer_ids )
      })
    
    !ATTRIBUTES.includes( slug ) && ATTRIBUTES.push( slug )
  } )

  return data
}

const
ATTRIBUTES = [],
QUESTIONNAIRES = {
  ...formatQuestions('neck', require('../../flowchart.data/questions/neck.json') ),
  ...formatQuestions('back', require('../../flowchart.data/questions/back.json') ),
  ...formatQuestions('elbow', require('../../flowchart.data/questions/elbow.json') ),
  ...formatQuestions('shoulder', require('../../flowchart.data/questions/shoulder.json') ),
  ...formatQuestions('foot-ankle', require('../../flowchart.data/questions/foot-ankle.json') ),
  ...formatQuestions('hand-wrist', require('../../flowchart.data/questions/hand-wrist.json') ),
  ...formatQuestions('knee-lower-leg', require('../../flowchart.data/questions/knee-lower-leg.json') ),
  ...formatQuestions('pelvis-hip-thigh', require('../../flowchart.data/questions/pelvis-hip-thigh.json') )
},
DIAGNOSIS = {
  neck: formatDiagnosis('neck', require('../../flowchart.data/diagnosis/neck.json') ),
  back: formatDiagnosis('back', require('../../flowchart.data/diagnosis/back.json') ),
  elbow: formatDiagnosis('elbow', require('../../flowchart.data/diagnosis/elbow.json') ),
  shoulder: formatDiagnosis('shoulder', require('../../flowchart.data/diagnosis/shoulder.json') ),
  'foot-ankle': formatDiagnosis('foot-ankle', require('../../flowchart.data/diagnosis/foot-ankle.json') ),
  'hand-wrist': formatDiagnosis('hand-wrist', require('../../flowchart.data/diagnosis/hand-wrist.json') ),
  'knee-lower-leg': formatDiagnosis('knee-lower-leg', require('../../flowchart.data/diagnosis/knee-lower-leg.json') ),
  'pelvis-hip-thigh': formatDiagnosis('pelvis-hip-thigh', require('../../flowchart.data/diagnosis/pelvis-hip-thigh.json') )
}

fs.writeFileSync('./src/data/questionnaires.json', JSON.stringify( QUESTIONNAIRES, null, 2 ), 'utf-8')
fs.writeFileSync('./src/data/diagnosis.json', JSON.stringify( DIAGNOSIS, null, 2 ), 'utf-8')
fs.writeFileSync('./src/data/attributes.json', JSON.stringify( ATTRIBUTES, null, 2 ), 'utf-8')