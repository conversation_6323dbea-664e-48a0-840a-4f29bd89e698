
const fs = require('fs')
const ch = require('cheerio')
const fetch = require('node-fetch')
const ATTRIBUTES = require('../data/attributes.json')
const CATALOGUE = {}

function getLink( attribute ){
  return `https://upswinghealth.com/conditions/${attribute}/`
}

function fetchContent( attribute ){
  return new Promise( ( resolve, reject ) => {
    const options = {
      method: 'GET'
    }
    
    fetch( getLink( attribute ), options )
    .then( res => res.text() )
    .then( resolve )
    .catch( reject )
  } )
}

function clean( str ){
  if( !str ) return ''

  return str.replace(/(\s?\t+)$/g, '')
            .replace(/(\s?\n+)$/g, '')
            .replace(/^(\n+)/g, '')
            .replace(/^(\t+)/g, '')
}

async function crawl( attribute ){

  const body = await fetchContent( attribute )
  if( !body ) return console.error('Failed to fetch body')
  
  const $ = ch.load( body )

  CATALOGUE[ attribute ] = {
    link: getLink( attribute ),
    content: {
      image: $('img.condition-hero').attr('src'),
      title: clean( $('.section--hero h2.section__heading').text() ),
      description: clean( $('.section--hero h3.section__sub-heading').text() ),
      overview: {
        body: clean( $('#general-information .body-text').html() )
      },
      causes: {
        title: clean( $('#causes .section__heading').text() ),
        body: clean( $('#causes .body-text').html() )
      },
      symptoms: {
        title: clean( $('#symptoms .section__heading').text() ),
        body: clean( $('#symptoms .body-text').html() )
      },
      consultDoctor: {
        title: clean( $('#consult-doctor .section__heading').text() ),
        body: clean( $('#consult-doctor .body-text').html() )
      },
      noOperativeTreatement: {
        title: clean( $('#non-operative-treatment .section__heading').text() ),
        body: clean( $('#non-operative-treatment .body-text').html() )
      },
      surgicalTreatement: {
        title: clean( $('#surgical-treatment .section__heading').text() ),
        body: clean( $('#surgical-treatment .body-text').html() )
      },
      recovery: {
        title: clean( $('#recovery .section__heading').text() ),
        body: clean( $('#recovery .body-text').html() )
      },
      sources: clean( $('.section--sources .sources__links').html() )
    }
  }

  console.log(`Crawling <${attribute}> ...`, CATALOGUE[ attribute ] )
  
  // await wait( 1 )
  try { 
    ATTRIBUTES.length && await crawl( ATTRIBUTES.shift() )
    console.log(`Finished with <${attribute}>.`)
  }
  catch( error ){ console.log(`Crawling ${attribute} failed: `, error ) }
}

function wait( delay ){
  return new Promise( resolve => {
    setTimeout( resolve, delay * 1000 )
  } )
}

;( async () => {
  // TEST: console.log( await crawl('mechanical-or-postural-low-back-pain') )
  
  await crawl( ATTRIBUTES[0] )
  fs.writeFileSync('./src/data/catalogue.json', JSON.stringify( CATALOGUE, null, 2 ), 'utf-8')
} )()