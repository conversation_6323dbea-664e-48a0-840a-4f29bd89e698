
export function toCapitalCase( str ){
  // Fonction de capitalisation du premier caractère d'un mot
  str.toLowerCase()

  const
  First = str.charAt(0),
  regex = new RegExp('^'+ First )

  return First.toUpperCase() + str.split( regex )[1]
}

export function getDeepValue( obj, key ){
  if( !typeof obj === 'object' ) null

  obj = JSON.parse( JSON.stringify( obj ) )

  const [ next, ...rest ] = key.split('.')
  if( !next ) return null

  if( Array.isArray( rest ) && rest.length )
    return getDeepValue( obj[ next ], rest.join('.') )

  return obj[ next ]
}

export function includeObject( array, item, matches ){
  return array.filter( each => {
    if( typeof each !== 'object' || typeof item !== 'object' ) 
      return false
    
    for( const x in matches ){
      const
      a = getDeepValue( each, matches[x] ),
      b = getDeepValue( item, matches[x] )

      if( !a || !b || a !== b ) return false
    }

    return true
  } ).length > 0
}