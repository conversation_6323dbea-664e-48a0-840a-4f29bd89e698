
import IOF from 'iframe.io'

const
SANDBOX_RULES = ['allow-scripts', 'allow-same-origin'],
REQUIRED_FEATURES = ['geolocation']

export class OSC {

  constructor( container, options ){
    if( !container ) throw new Error('Undefined HTML Element Container ID')
    if( !options ) throw new Error('Undefined Configuration')
    if( !options.clientId ) throw new Error('Invalid credentials. <clientId> expected')
    if( !options.accessToken ) throw new Error('Invalid credentials. <accessToken> expected')

    this.clientId = options.clientId
    this.accessToken = options.accessToken

    this.readyCallback = options.ready
    this.errorCallback = options.error
    this.failedCallback = options.failed
    this.doneCallback = options.done

    this.isDev = options.dev || false
    this.baseURL = options.dev ? 'http://localhost:5000' : 'https://osc.upswinghealth.io'

    this.chn = false
    this.isConnected = false

    function load(){
      // Embed osc
      this.container = document.getElementById( container )
      if( !this.container ) throw new Error(`HTML Element Container <#${container}> Not Found`)
      
      this.container.innerHTML = `<iframe id="osc-sdk"
                                          src="${this.baseURL}?embed=1"
                                          style="width:100%;height:100%;border:none;"
                                          title="Orthopedic Symptom Checker"
                                          importance="high"
                                          referrerpolicy="origin"
                                          allow="${REQUIRED_FEATURES.join()}"
                                          sandbox="${SANDBOX_RULES.join(' ')}"></iframe>`

      document.getElementById('osc-sdk').onload = this.onload.bind( this )
    }

    function networkError(){
      const errmess = {
        status: 'SERVICE_UNAVAILABLE',
        message: 'Internet network problem'
      }

      if( typeof this.errorCallback == 'function' ) this.errorCallback( errmess )
      else throw new Error( errmess.message )
    }

    // Check remote server availability
    window
    .fetch( this.baseURL, { mode: 'no-cors' })
    .then( load.bind( this ) )
    .catch( networkError.bind( this ) )
  }

  onload( e ){
    // Remove all previous listeners when iframe reloaded
    this.chn && this.chn.removeListeners()

    this.chn = new IOF({ type: 'window', debug: this.isDev })
    this.chn.initiate( e.target.contentWindow, this.baseURL )

    this.chn
    .once('connect', () => {
      this.chn.emit('authenticate', { clientId: this.clientId, accessToken: this.accessToken }, ( error, tenant ) => {
        if( error ){
          if( typeof this.errorCallback == 'function' )
            return this.errorCallback( error )
          else throw new Error( error.message )
        }

        this.isConnected = true

        // Set initial patient dataset
        this.patient && this.setPatient( this.patient );

        console.log(this.patient,'patient');

        typeof this.readyCallback == 'function'
        && this.readyCallback( tenant )
      })
    })
    .on('osc:failed', error => {
      typeof this.failedCallback == 'function'
      && this.failedCallback( error )
    })
    .on('osc:done', transaction => {
      typeof this.doneCallback == 'function'
      && this.doneCallback( transaction )
    })
  }
  isReady(){ return this.chn && this.isConnected }

  ready( fn ){
    this.readyCallback = fn
    return this
  }
  error( fn ){
    this.errorCallback = fn
    return this
  }
  failed( fn ){
    this.failedCallback = fn
    return this
  }
  done( fn ){
    this.doneCallback = fn
    return this
  }

  setPatient( dataset ){
    if( !this.isReady() ) return

    this.patient = {
      ...( dataset || {}), // new or update patient dataset
      ...this.patient, // Merge with existing/initial patient dataset
    }

    this.chn.emit('osc:patient', this.payload )
  }
  open( block ){ this.isReady() && this.chn.emit('osc:goto', block ) }
}

export default window.SDK = { OSC }