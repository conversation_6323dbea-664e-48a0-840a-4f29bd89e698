apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: symptomchecker
  name: fullstack
  labels:
    app: fullstack
spec:
  replicas: 1
  selector:
    matchLabels:
      app: fullstack
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: fullstack
    spec:
      containers:
      - name: fullstack
        image: symptomchecker
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 4000
          name: http
        env:              
        - name: DATABASE_URL
          value: mongodb+srv://uh-mdp-manager:<EMAIL>/admin?authSource=admin&replicaSet=db-mongodb-usa-nyc1-001&tls=true


---
apiVersion: v1
kind: Service
metadata:
  namespace: symptomchecker
  name: fullstack
  labels:
    app: fullstack
spec:
  selector:
    app: fullstack
  type: NodePort
  ports:
    - name: http
      port: 4000
      targetPort: 4000