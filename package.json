{"name": "symptom-checker", "version": "1.0.0", "private": true, "scripts": {"start": "cd ./.sandbox && yarn start", "start:prod": "NODE_ENV=production node ./.sandbox/build/server.js", "build": "cd ./.sandbox && yarn build --noninteractive", "test": "cd ./.sandbox && yarn test:dev"}, "main": "src/index.marko", "workspaces": [".sandbox"], "license": "MIT", "dependencies": {"@fabrice8/drawflow": "^0.0.59", "@marko-tags/subscribe": "^0.4.3", "backbone": "^1.4.1", "boxicons": "^2.1.4", "cheerio": "^1.0.0-rc.12", "dagre": "^0.8.5", "filefy": "^0.1.11", "gojs": "^2.3.5", "iframe.io": "^1.0.1", "jointjs": "^3.7.2", "jquery": "^3.6.3", "markojs-form": "^1.0.5", "moment": "^2.29.4", "mongodb": "^6.3.0", "node-fetch": "2.6.1", "vanillajs-datepicker": "^1.3.1"}}